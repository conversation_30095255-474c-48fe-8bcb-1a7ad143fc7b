# Upload Tuner 系统集成测试需求文档

## 1. 测试概述

### 1.1 测试目的
本文档旨在指导测试人员对 Upload Tuner 系统进行全面的集成测试，确保系统各组件之间的交互正常，功能完整，性能稳定。

### 1.2 测试范围
本次测试涵盖 Upload Tuner 系统的所有主要组件，包括：
- ETCD 集群
- Upload Tuner 服务
- ETCD 管理系统前端
- Nginx 代理配置

### 1.3 测试环境要求
- 操作系统：Linux
- ETCD 版本：v3.3.11 或更高版本
- Nginx 版本：1.18 或更高版本
- 网络环境：内部测试网络，确保各组件间通信畅通

## 2. 测试项目

### 2.1 ETCD 集群测试

#### 2.1.1 集群部署测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 单节点部署 | 使用脚本启动单节点 ETCD | 节点成功启动，可通过 API 访问 |
| 多节点部署 | 使用脚本启动 3 节点 ETCD 集群 | 集群成功启动，节点间可正常通信 |
| 自定义数据目录 | 使用 `-d` 参数指定数据目录 | 数据正确存储在指定目录 |
| 自定义日志目录 | 使用 `-l` 参数指定日志目录 | 日志正确输出到指定目录 |
| 帮助信息 | 使用 `-h` 或 `--help` 参数 | 正确显示帮助信息 |

#### 2.1.2 集群功能测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 键值读写 | 通过 API 写入和读取键值 | 数据正确写入和读取 |
| 集群健康状态 | 检查集群健康状态 | 返回所有节点健康状态 |
| 节点故障恢复 | 模拟单节点故障并恢复 | 集群能继续工作，故障节点恢复后能重新加入集群 |

### 2.2 Upload Tuner 服务测试

#### 2.2.1 基础功能测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 服务启动 | 启动 Upload Tuner 服务 | 服务成功启动，无错误日志 |
| 健康检查 | 访问 `/health` 端点 | 返回 200 状态码和健康状态 |
| Ping 测试 | 访问 `/ping` 端点 | 返回 200 状态码和 pong 响应 |
| 版本信息 | 访问 `/version` 端点 | 返回正确的版本信息 |

#### 2.2.2 节点分配功能测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 用户节点分配 | 请求 `/user/node/{userId}` | 返回合适的节点信息 |
| 应用节点分配 | 请求 `/app/node/{appId}` | 返回合适的节点信息 |
| 管理员查看节点 | 请求 `/admin/nodes` | 返回所有节点信息 |
| 无效用户ID | 请求不存在的用户ID | 返回适当的错误信息 |

#### 2.2.3 指标上报功能测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 节点指标上报 | 向 `/metrics/{nodeId}` 发送指标数据 | 数据成功上报，返回成功状态 |
| 无效API密钥 | 使用无效的 API 密钥上报指标 | 返回 401 错误 |
| 无效节点ID | 向不存在的节点ID上报指标 | 返回适当的错误信息 |

### 2.3 ETCD 管理系统前端测试

#### 2.3.1 界面功能测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 前端加载 | 访问管理系统首页 | 页面正确加载，无JS错误 |
| 节点管理页面 | 访问节点管理页面 | 显示所有节点信息 |
| 应用密钥页面 | 访问应用密钥页面 | 显示所有应用密钥信息 |

#### 2.3.2 节点管理功能测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 查看节点列表 | 在节点管理页面查看节点 | 正确显示所有节点 |
| 添加节点 | 添加新节点 | 节点成功添加并显示在列表中 |
| 编辑节点 | 编辑现有节点信息 | 节点信息成功更新 |
| 删除节点 | 删除现有节点 | 节点成功从列表中移除 |

#### 2.3.3 应用密钥管理功能测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 查看密钥列表 | 在应用密钥页面查看密钥 | 正确显示所有应用密钥 |
| 添加密钥 | 添加新应用密钥 | 密钥成功添加并显示在列表中 |
| 编辑密钥 | 编辑现有密钥信息 | 密钥信息成功更新 |
| 删除密钥 | 删除现有密钥 | 密钥成功从列表中移除 |

### 2.4 Nginx 代理测试

#### 2.4.1 Metrics API 代理测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 健康检查 | 访问 `/health` 端点 | 返回 200 状态码 |
| 节点注册 | 通过代理向 ETCD 注册节点 | 节点成功注册 |
| 指标上报 | 通过代理上报节点指标 | 指标成功上报 |
| API 密钥验证 | 使用有效和无效的 API 密钥 | 有效密钥通过，无效密钥被拒绝 |

#### 2.4.2 ETCD 管理系统代理测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 静态文件访问 | 访问前端静态文件 | 文件正确加载 |
| ETCD API 代理 | 通过代理访问 ETCD API | 请求正确转发并返回结果 |
| CORS 支持 | 从不同域发送请求 | CORS 头部正确设置，请求成功 |

#### 2.4.3 节点分配 API 代理测试
| 测试项 | 测试内容 | 预期结果 |
|-------|---------|---------|
| 用户节点分配 | 通过代理请求用户节点 | 请求正确转发并返回节点 |
| 应用节点分配 | 通过代理请求应用节点 | 请求正确转发并返回节点 |
| 管理员查看节点 | 通过代理查看所有节点 | 请求正确转发并返回所有节点 |

## 3. 集成测试场景

### 3.1 端到端流程测试

#### 3.1.1 节点注册与分配流程
1. 启动 ETCD 集群
2. 启动 Upload Tuner 服务
3. 通过 Nginx 代理向 ETCD 注册新节点
4. 通过 Upload Tuner 的用户节点分配接口获取节点
5. 验证分配的节点是否正确

#### 3.1.2 指标上报与查看流程
1. 通过 Nginx 代理向 Upload Tuner 上报节点指标
2. 验证指标是否正确存储
3. 通过管理接口查看节点状态
4. 验证节点状态是否反映了上报的指标

#### 3.1.3 管理系统操作流程
1. 访问 ETCD 管理系统前端
2. 添加新的应用密钥
3. 使用新密钥通过 API 进行操作
4. 验证操作是否成功

### 3.2 故障恢复测试

#### 3.2.1 ETCD 节点故障恢复
1. 模拟单个 ETCD 节点故障
2. 验证系统是否继续正常工作
3. 恢复故障节点
4. 验证节点是否成功重新加入集群

#### 3.2.2 Upload Tuner 服务故障恢复
1. 停止 Upload Tuner 服务
2. 重启服务
3. 验证服务是否恢复正常功能
4. 验证之前的数据是否完整保留

#### 3.2.3 Nginx 代理故障恢复
1. 停止 Nginx 服务
2. 重启服务
3. 验证所有代理功能是否恢复正常
