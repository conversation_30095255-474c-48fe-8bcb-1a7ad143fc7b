# Upload Tuner API 文档

本文档描述了 Upload Tuner 服务提供的 API 接口。

## 目录

- [基础信息](#基础信息)
- [用户接口](#用户接口)
  - [获取节点地址 (用户)](#获取节点地址-用户)
  - [获取节点地址 (应用)](#获取节点地址-应用)
- [节点接口](#节点接口)
  - [注册&&发送心跳](#注册发送心跳)
  - [更新节点指标](#更新节点指标)
- [管理接口](#管理接口)
  - [健康检查](#健康检查)
  - [版本信息](#版本信息)
  - [Ping 测试](#ping-测试)
  - [获取所有节点](#获取所有节点)

## 基础信息

- 基础 URL: `/`
- 所有接口返回 JSON 格式数据
- 除特殊说明外，所有错误响应均为标准 HTTP 状态码及相应的错误信息
- 认证方式：
  - 用户接口：JWT令牌认证（Bearer Token）
  - 应用接口：基于时间戳和应用密钥的签名认证

## 用户接口

### 获取节点地址 (用户)

根据用户 ID 和 JWT 令牌获取分配的节点地址。

- **URL**: `/user/node/:userid`
- **方法**: `GET`
- **URL 参数**: 
  - `userid`: 用户 ID，唯一标识一个用户
- **请求头**:
  - `Authorization`: Bearer 格式的 JWT 令牌，必需参数。格式为 `Bearer <token>`
  - JWT 令牌必须包含 `user_id` 声明，并且必须与 URL 中的 `userid` 参数匹配
- **成功响应**:
  - 状态码: `200 OK`
  - 内容示例:
    ```json
    {
      "node_address": "*************:8080"
    }
    ```
- **错误响应**:
  - 状态码: `400 Bad Request`
    - 缺失必要的路径参数 `userid`
    - 无效的 Authorization 头格式
  - 状态码: `401 Unauthorized`
    - 缺失 Authorization 头
    - 无效或过期的令牌
    - 令牌中的用户 ID 与请求路径中的用户 ID 不匹配
  - 状态码: `503 Service Unavailable`
    - 无可用节点
  - 状态码: `500 Internal Server Error`
    - 服务器内部错误

### 获取节点地址 (应用)

根据应用 ID 获取分配的节点地址。

- **请求示例**:
  ```
  GET /app/node/app123456?token=abcdef123456&ts=1715500000&cip=************
  ```

- **URL**: `/app/node/:appid`
- **方法**: `GET`
- **URL 参数**:
  - `appid`: 应用 ID，唯一标识一个应用渠道
- **查询参数**:
  - `token`: 应用认证令牌，必需参数
  - `ts`: 截止时间戳（Unix 时间戳，秒级），必需参数
  - `cip`: 客户端 IP 地址，必需参数
- **认证机制**:
  - 截止时间戳必须在当前时间的时间戳，否则请求将被拒绝
  - 使用应用 ID、时间戳和应用密钥生成的签名进行认证
- **成功响应**:
  - 状态码: `200 OK`
  - 内容示例:
    ```json
    {
      "node_address": "*************:8080"
    }
    ```
- **错误响应**:
  - 状态码: `400 Bad Request`
    - 缺失必要的路径参数 `appid`
    - 缺失必要的查询参数 `token`、`ts` 或 `cip`
    - 无效的时间戳格式
  - 状态码: `401 Unauthorized`
    - 请求链接已过期（时间戳超过 120 秒）
    - 应用认证失败（无效的应用 ID 或令牌）
  - 状态码: `503 Service Unavailable`
    - 无可用节点
  - 状态码: `500 Internal Server Error`
    - 服务器内部错误

## 节点接口

### 注册&&发送心跳

用于注册节点或更新节点信息（包括刷新 TTL）。如果节点已存在，则更新其值和 TTL。**注册与心跳使用同一个接口**
节点通过 Nginx 代理访问 etcd v2 API 进行注册和心跳维持。客户端需要直接与 Nginx 代理交互，并遵循 etcd v2 的 API 规范。

- **URL**: `/v2/keys/nodes/{node_id}`
  - `{node_id}`: 节点的唯一标识符。
- **方法**: `PUT`
- **头部**: 
  - `API-Key: <your_secret_api_key>` (必需) - 用于 Nginx 代理的身份验证。
  - `Content-Type: application/x-www-form-urlencoded` (当使用 etcd v2 API 时，通常参数在 body 中以 form 形式传递)
- **请求体参数 (form-urlencoded)**:
  - `value` (必需): 包含节点信息的 JSON 字符串。例如：
    ```json
    {
      "id": "node-123",
      "ip": "*************",
      "port": 8080,
      "capacity": 100
    }
    ```
  - `ttl` (可选, 但强烈推荐): 节点的生存时间（秒）。节点必须在此时间内发送心跳（即重新发送带 TTL 的 `PUT` 请求）以保持注册状态。例如: `ttl=30`。

- **示例请求 (使用 curl)**:
  ```bash
  curl -X PUT http://localhost:8080/v2/keys/nodes/node-123 \
  -H "API-Key: secret_key_123" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d 'value={"id":"node-123","ip":"*************","port":8080,"capacity":100}' \
  -d 'ttl=30'
  ```

- **成功响应 (etcd v2 format)**:
  - 状态码: `200 OK` (更新) 或 `201 Created` (新建)
  - 内容示例:
    ```json
    {
        "action": "set",
        "node": {
            "key": "/nodes/node-123",
            "value": "{\"id\":\"node-123\",\"ip\":\"*************\",\"port\":8080,\"capacity\":100}",
            "expiration": "2025-05-07T08:30:00.123Z", // 示例过期时间
            "ttl": 30, // 剩余 TTL
            "modifiedIndex": 5,
            "createdIndex": 5
        }
    }
    ```

- **错误响应**:
  - 状态码: `401 Unauthorized` (由 Nginx 返回)
    ```json
    {"error": "Invalid or missing API key"}
    ```
  - 状态码: `403 Forbidden` (由 Nginx 返回，如果操作不允许，但对于 `PUT /v2/keys/nodes/*` 应该允许)
    ```json
    {"error": "Operation not permitted"}
    ```
  - 其他 etcd v2 API 可能返回的错误状态码 (例如 `400 Bad Request` 如果请求体无效)。


### 更新节点指标

更新指定节点的指标数据。该接口用于节点向调度服务上报其当前负载和状态信息。

- **URL**: `/metrics/:nodeID`
- **方法**: `POST`
- **URL 参数**:
  - `nodeID`: 节点 ID，必需参数，唯一标识一个节点
- **请求体**:
  - 必须为 JSON 格式，符合 `NodeMetrics` 模型结构
  - 示例:
    ```json
    {
      "cpu_usage": 0.5,
      "memory_usage": 0.6,
      "disk_usage": 0.3,
      "disk_free": 250.5,
      "concurrent_tasks": 100,
      "load_1min": 1.25,
      "updated_at": "2025-05-07T15:29:40+08:00"
    }
    ```
  - **字段说明**:
    - `cpu_usage` (float64): CPU 使用率（百分比, 0.0 到 1.0）
    - `memory_usage` (float64): 内存使用率（百分比, 0.0 到 1.0）
    - `disk_usage` (float64): 磁盘使用率（百分比, 0.0 到 1.0）
    - `disk_free` (float64): 磁盘剩余可用空间 (GB)
    - `concurrent_tasks` (int): 当前并发任务数
    - `load_1min` (float64): 1分钟平均负载
    - `updated_at` (string): 指标更新时间 (RFC3339 格式, 例如 "2025-05-07T15:29:40+08:00")
- **成功响应**:
  - 状态码: `200 OK`
  - 内容示例:
    ```json
    {
      "status": "Metrics recorded successfully"
    }
    ```
- **错误响应**:
  - 状态码: `400 Bad Request`
    - 缺失必要的路径参数 `nodeID`
    - 无效的指标数据格式
  - 状态码: `500 Internal Server Error`
    - 服务器内部错误或无法记录指标

## 管理接口

### 健康检查

检查服务是否正常运行。

- **URL**: `/health`
- **方法**: `GET`
- **成功响应**:
  - 状态码: `200 OK`
  - 内容示例:
    ```json
    {
      "status": "UP"
    }
    ```

### 版本信息

获取服务的版本信息。

- **URL**: `/version`
- **方法**: `GET`
- **成功响应**:
  - 状态码: `200 OK`
  - 内容示例:
    ```json
    {
      "commit": "a1b2c3d4e5f6",
      "tag": "v1.0.0",
      "build_time": "2025-05-07T08:00:00Z",
      "start_time": "2025-05-07T08:30:00Z"
    }
    ```

### Ping 测试

简单的连通性测试。

- **URL**: `/ping`
- **方法**: `GET`
- **成功响应**:
  - 状态码: `200 OK`
  - 内容示例:
    ```json
    {
      "message": "pong api"
    }
    ```
- **备注**:
  - 指标上报服务也提供相同的接口，但响应内容为 `{"message": "pong metrics"}`

### 获取所有节点

获取系统中所有注册的节点信息（管理接口）。

- **URL**: `/admin/nodes`
- **方法**: `GET`
- **成功响应**:
  - 状态码: `200 OK`
  - 内容示例:
    ```json
    [
      {
        "id": "node-123",
        "ip": "*************",
        "port": 8080,
        "capacity": 100
        "status": "active",
        "load": 0.75,
        "last_heartbeat": "2025-05-07T14:30:00Z"
      },
      {
        "id": "node2",
        "ip": "*************",
        "port": 8080,
        "status": "active",
        "load": 0.25,
        "last_heartbeat": "2025-05-07T14:45:00Z"
      }
    ]
    ```
- **错误响应**:
  - 状态码: `500 Internal Server Error` - 服务器内部错误