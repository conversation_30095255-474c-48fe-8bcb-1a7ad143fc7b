{"version": "0.2.0", "configurations": [{"name": "运行 etcd 集成测试", "type": "go", "request": "launch", "mode": "test", "program": "${workspaceFolder}/internal/services", "args": ["-test.v", "-test.run", "TestEtcdIntegration"], "env": {"ETCD_ENDPOINT": "http://192.168.16.61:8080", "ETCD_API_KEY": "secret_key_123"}}, {"name": "运行所有测试", "type": "go", "request": "launch", "mode": "test", "program": "${workspaceFolder}/...", "args": ["-test.v"]}, {"name": "运行 upload_tuner", "type": "go", "request": "launch", "mode": "debug", "program": "${workspaceFolder}/main.go"}]}