package logger

import (
	"os"

	"funshion.com/upload_tuner/configs"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var ( 
	// Log is the global logger instance.
	Log *zap.Logger
)

// InitLogger initializes the global logger based on the provided configuration.
func InitLogger(cfg configs.LogConfig) error {
	var cores []zapcore.Core

	// --- Setup Log Level ---
	logLevel := zap.NewAtomicLevel()
	if err := logLevel.UnmarshalText([]byte(cfg.Level)); err != nil {
		// Default to info level if parsing fails
		logLevel.SetLevel(zapcore.InfoLevel)
		// Use zap's global logger before initialization is complete for this warning
		zap.L().Warn("Invalid log level in config, defaulting to 'info'", zap.String("configured_level", cfg.Level), zap.Error(err))
	}

	// --- Setup Encoders ---
	// Encoder config for file logging (always JSON, no color)
	fileEncoderCfg := zap.NewProductionEncoderConfig()
	fileEncoderCfg.EncodeTime = zapcore.ISO8601TimeEncoder // Consistent time format
	jsonEncoder := zapcore.NewJSONEncoder(fileEncoderCfg)

	// Encoder for console logging (always Development style with color if enabled)
	var consoleEncoder zapcore.Encoder
	if cfg.Console { // Only configure console encoder if console output is enabled
		consoleEncoderCfg := zap.NewDevelopmentEncoderConfig()
		consoleEncoderCfg.EncodeTime = zapcore.ISO8601TimeEncoder
		consoleEncoderCfg.EncodeLevel = zapcore.CapitalColorLevelEncoder // Always use color for console
		consoleEncoder = zapcore.NewConsoleEncoder(consoleEncoderCfg)
	}

	// --- Setup Console Logging ---
	// Always setup console logging if cfg.Console is true
	if cfg.Console {
		consoleDebugging := zapcore.Lock(os.Stdout)
		core := zapcore.NewCore(consoleEncoder, consoleDebugging, logLevel) // Use the configured consoleEncoder
		cores = append(cores, core)
	}

	// --- Setup File Logging with Lumberjack ---
	if cfg.File.Enable {
		w := zapcore.AddSync(&lumberjack.Logger{
			Filename:   cfg.File.Path,
			MaxSize:    cfg.File.MaxSize,    // megabytes
			MaxBackups: cfg.File.MaxBackups,
			MaxAge:     cfg.File.MaxAge,     // days
			Compress:   cfg.File.Compress,
		})
		// Use the dedicated file encoder (jsonEncoder)
		core := zapcore.NewCore(jsonEncoder, w, logLevel) // Use JSON encoder for file
		cores = append(cores, core)
	}

	// Combine cores for multi-output
	combinedCore := zapcore.NewTee(cores...)

	// Build the logger
	logger := zap.New(combinedCore)

	// Add caller info if configured
	if cfg.ReportCaller {
		logger = logger.WithOptions(zap.AddCaller())
	}

	// Replace the global logger instance
	Log = logger
	// Also replace the standard log package's output (optional but helpful)
	zap.ReplaceGlobals(logger)

	Log.Info("Logger initialized", zap.String("level", cfg.Level), zap.Bool("console", cfg.Console), zap.Bool("file_enabled", cfg.File.Enable))

	return nil
}
