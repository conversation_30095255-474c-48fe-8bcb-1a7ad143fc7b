package services

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"funshion.com/upload_tuner/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.etcd.io/etcd/client/v2"
)

// 注意：NodePrefix 常量已在 etcd_service.go 中定义

// 创建带有预定义响应的 MockWatcher
func NewMockWatcher(responses []*client.Response) *MockWatcher {
	mw := &MockWatcher{}
	
	// 设置首次调用的返回值
	if len(responses) > 0 {
		mw.On("Next", mock.Anything).Return(responses[0], nil).Once()
		
		// 设置后续调用的返回值
		for i := 1; i < len(responses); i++ {
			mw.On("Next", mock.Anything).Return(responses[i], nil).Once()
		}
	}
	
	// 如果没有更多响应，返回上下文取消错误
	mw.On("Next", mock.Anything).Return(nil, context.Canceled).Maybe()
	
	return mw
}

// TestEtcdService_watchNodes 测试 watchNodes 功能
func TestEtcdService_watchNodes(t *testing.T) {
	// 创建测试节点数据
	testNode := models.Node{
		ID:       "test-node-1",
		IP:       "*************",
		Port:     8080,
		Capacity: 100,
	}
	
	nodeJSON, err := json.Marshal(testNode)
	require.NoError(t, err)
	
	// 创建模拟的 etcd 响应
	responses := []*client.Response{
		// 添加节点响应
		{
			Action: "set",
			Node: &client.Node{
				Key:   "/nodes/test-node-1",
				Value: string(nodeJSON),
			},
		},
		// 更新节点响应
		{
			Action: "update",
			Node: &client.Node{
				Key:   "/nodes/test-node-1",
				Value: string(nodeJSON),
			},
		},
		// 删除节点响应
		{
			Action: "delete",
			Node: &client.Node{
				Key: "/nodes/test-node-1",
			},
		},
	}
	
	// 创建模拟的 Watcher
	mockWatcher := NewMockWatcher(responses)
	
	// 创建模拟的 KeysAPI
	mockKapi := new(MockKeysAPI)
	mockKapi.On("Watcher", NodePrefix, mock.Anything).Return(mockWatcher)
	
	// 创建 EtcdService 实例
	service := &EtcdService{
		kapi: mockKapi,
	}
	
	// 创建通道来接收回调结果
	nodeEvents := make(chan struct {
		node models.Node
		action string
	}, 10)
	
	// 注册回调函数
	service.RegisterNodeCallback(func(node models.Node, action string) {
		nodeEvents <- struct {
			node models.Node
			action string
		}{node, action}
	})
	
	// 启动 watchNodes
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	go service.watchNodes(ctx)
	
	// 验证回调是否被正确触发
	// 1. 添加节点事件
	select {
	case event := <-nodeEvents:
		assert.Equal(t, "set", event.action)
		assert.Equal(t, "test-node-1", event.node.ID)
		assert.Equal(t, "*************", event.node.IP)
	case <-time.After(2 * time.Second):
		t.Fatal("没有收到节点添加事件")
	}
	
	// 2. 更新节点事件
	select {
	case event := <-nodeEvents:
		assert.Equal(t, "update", event.action)
		assert.Equal(t, "test-node-1", event.node.ID)
	case <-time.After(2 * time.Second):
		t.Fatal("没有收到节点更新事件")
	}
	
	// 3. 删除节点事件
	select {
	case event := <-nodeEvents:
		assert.Equal(t, "delete", event.action)
		assert.Equal(t, "test-node-1", event.node.ID)
	case <-time.After(2 * time.Second):
		t.Fatal("没有收到节点删除事件")
	}
	
	// 验证 watcher 被正确调用
	mockKapi.AssertExpectations(t)
}
