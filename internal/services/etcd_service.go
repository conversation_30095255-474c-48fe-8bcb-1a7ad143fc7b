package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"funshion.com/upload_tuner/internal/models"
	"go.etcd.io/etcd/client/v2"
	"go.uber.org/zap"
)

const (
	NodePrefix = "/nodes/"
	etcdAppSecretPrefix = "/app_secrets/"
	defaultTTL          = 60
)

type NodeChangeCallback func(node models.Node, action string)

type AppSecretEventCallback func(key string, value string, action string)

type EtcdService struct {
	client               client.Client
	kapi                 client.KeysAPI
	endpoints            []string
	nodeWatchCancel      context.CancelFunc
	appSecretWatchCancel context.CancelFunc
	watchMutex           sync.Mutex
	nodeCallbacks        []NodeChangeCallback
	appSecretCallbacks   []AppSecretEventCallback
	mu                   sync.RWMutex
}

func NewEtcdService(endpoints []string) (*EtcdService, error) {
	if len(endpoints) == 0 {
		endpoints = []string{
			"http://127.0.0.1:2379",
			"http://127.0.0.1:2381",
			"http://127.0.0.1:2383",
		}
	}

	cfg := client.Config{
		Endpoints:               endpoints,
		Transport:               client.DefaultTransport,
		HeaderTimeoutPerRequest: time.Second * 5,
	}

	c, err := client.New(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建etcd客户端失败: %v", err)
	}

	kapi := client.NewKeysAPI(c)

	s := &EtcdService{
		client:             c,
		kapi:               kapi,
		endpoints:          endpoints,
		nodeCallbacks:      make([]NodeChangeCallback, 0),
		appSecretCallbacks: make([]AppSecretEventCallback, 0),
	}

	nodeCtx, nodeCancel := context.WithCancel(context.Background())
	s.nodeWatchCancel = nodeCancel
	go s.watchNodes(nodeCtx)

	appSecretCtx, appSecretCancel := context.WithCancel(context.Background())
	s.appSecretWatchCancel = appSecretCancel
	go s.watchAppSecrets(appSecretCtx)

	return s, nil
}

func (s *EtcdService) RegisterNodeCallback(callback NodeChangeCallback) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.nodeCallbacks = append(s.nodeCallbacks, callback)
	zap.S().Info("Node change callback registered.")
}

func (s *EtcdService) RegisterAppSecretCallback(callback AppSecretEventCallback) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.appSecretCallbacks = append(s.appSecretCallbacks, callback)
	zap.S().Info("App secret event callback registered.")
}

func (s *EtcdService) watchNodes(ctx context.Context) {
	zap.S().Info("Node watcher started.")
	watcher := s.kapi.Watcher(NodePrefix, &client.WatcherOptions{Recursive: true})
	if watcher == nil {
		zap.S().Error("Failed to create node watcher")
		return
	}

	for {
		resp, err := watcher.Next(ctx)
		if err != nil {
			if errors.Is(err, context.Canceled) {
				zap.S().Info("Node watcher stopped gracefully.")
				return
			}
			zap.S().Errorf("Node watcher error: %v. Retrying watcher...", err)
			time.Sleep(5 * time.Second)
			watcher = s.kapi.Watcher(NodePrefix, &client.WatcherOptions{Recursive: true})
			if watcher == nil {
				zap.S().Error("Failed to recreate node watcher after error")
				return
			}
			continue
		}

		if resp.Node == nil {
			zap.S().Warn("Received nil node in node watcher response")
			continue
		}

		if resp.Node.Dir {
			zap.S().Debugf("Ignoring directory change in node watcher: %s", resp.Node.Key)
			continue
		}

		var node models.Node
		action := resp.Action
		nodeKey := resp.Node.Key

		if action == "set" || action == "create" || action == "update" {
			if err := json.Unmarshal([]byte(resp.Node.Value), &node); err != nil {
				zap.S().Errorf("Failed to unmarshal node data from key %s: %v", nodeKey, err)
				continue
			}
			zap.S().Infof("Node change detected: Action=%s, Key=%s, Value=%+v", action, nodeKey, node)
		} else if action == "delete" || action == "expire" {
			node.ID = strings.TrimPrefix(nodeKey, NodePrefix)
			zap.S().Infof("Node change detected: Action=%s, Key=%s", action, nodeKey)
		} else {
			zap.S().Warnf("Unhandled action in node watcher: %s for key %s", action, nodeKey)
			continue
		}

		s.mu.RLock()
		callbacks := make([]NodeChangeCallback, len(s.nodeCallbacks))
		copy(callbacks, s.nodeCallbacks)
		s.mu.RUnlock()

		for _, cb := range callbacks {
			cb(node, action)
		}
	}
}

func (s *EtcdService) watchAppSecrets(ctx context.Context) {
	zap.S().Info("App secret watcher starting.")
	watcher := s.kapi.Watcher(etcdAppSecretPrefix, &client.WatcherOptions{Recursive: true})
	if watcher == nil {
		zap.S().Error("Failed to create app secret watcher")
		return
	}

	for {
		resp, err := watcher.Next(ctx)
		if err != nil {
			if errors.Is(err, context.Canceled) {
				zap.S().Info("App secret watcher stopped gracefully.")
				return
			}
			zap.S().Errorf("App secret watcher error: %v. Retrying watcher...", err)
			time.Sleep(5 * time.Second)
			watcher = s.kapi.Watcher(etcdAppSecretPrefix, &client.WatcherOptions{Recursive: true})
			if watcher == nil {
				zap.S().Error("Failed to recreate app secret watcher after error")
				return
			}
			continue
		}

		if resp.Node == nil {
			zap.S().Warn("Received nil node in app secret watcher response")
			continue
		}

		if resp.Node.Dir {
			zap.S().Debugf("Ignoring directory change in app secret watcher: %s", resp.Node.Key)
			continue
		}

		zap.S().Debugf("App secret change detected: Action=%s, Key=%s", resp.Action, resp.Node.Key)

		s.mu.RLock()
		callbacks := make([]AppSecretEventCallback, len(s.appSecretCallbacks))
		copy(callbacks, s.appSecretCallbacks)
		s.mu.RUnlock()

		for _, cb := range callbacks {
			cb(resp.Node.Key, resp.Node.Value, resp.Action)
		}
	}
}

func (s *EtcdService) Close() {
	s.watchMutex.Lock()
	defer s.watchMutex.Unlock()

	if s.nodeWatchCancel != nil {
		zap.S().Info("Closing node watcher...")
		s.nodeWatchCancel()
		s.nodeWatchCancel = nil
	}
	if s.appSecretWatchCancel != nil {
		zap.S().Info("Closing app secret watcher...")
		s.appSecretWatchCancel()
		s.appSecretWatchCancel = nil
	}
	zap.S().Info("EtcdService closed.")
}

func (s *EtcdService) LoadAllNodes() ([]models.Node, error) {
	resp, err := s.kapi.Get(context.Background(), NodePrefix, &client.GetOptions{
		Recursive: true,
	})
	if err != nil {
		if client.IsKeyNotFound(err) {
			return []models.Node{}, nil
		}
		return nil, fmt.Errorf("从etcd加载节点数据失败: %v", err)
	}

	var nodes []models.Node
	if resp.Node.Dir {
		for _, node := range resp.Node.Nodes {
			var nodeData models.Node
			if err := json.Unmarshal([]byte(node.Value), &nodeData); err != nil {
				log.Printf("解析节点数据失败: %v, 数据: %s", err, node.Value)
				continue
			}
			nodes = append(nodes, nodeData)
		}
	}

	return nodes, nil
}

func (s *EtcdService) LoadAllAppSecretKeys() ([]models.AppSecrets, error) {
	resp, err := s.kapi.Get(context.Background(), etcdAppSecretPrefix, &client.GetOptions{
		Recursive: true,
	})
	if err != nil {
		if client.IsKeyNotFound(err) {
			return []models.AppSecrets{}, nil
		}
		return nil, fmt.Errorf("从etcd加载应用密钥失败: %v", err)
	}

	var appSecrets []models.AppSecrets
	if resp.Node.Dir {
		for _, node := range resp.Node.Nodes {
			var appSecret models.AppSecrets
			if err := json.Unmarshal([]byte(node.Value), &appSecret); err != nil {
				log.Printf("解析应用密钥数据失败: %v, 数据: %s", err, node.Value)
				continue
			}
			appSecrets = append(appSecrets, appSecret)
		}
	}

	return appSecrets, nil
}
