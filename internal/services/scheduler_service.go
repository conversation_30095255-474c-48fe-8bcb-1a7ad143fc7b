package services

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"strings"
	"sync"
	"time"

	"funshion.com/upload_tuner/configs"
	"funshion.com/upload_tuner/internal/models"
	"funshion.com/upload_tuner/ipdb"
	"go.uber.org/zap"
)

// 调度器错误定义
var (
	// ErrNoNodes is returned when no nodes are available for scheduling.
	ErrNoNodes = errors.New("no nodes available")
	// ErrNoOperatorFound is returned when no operator is found for the client IP.
	ErrNoOperatorFound = errors.New("no operator found for client IP")
	// ErrNoNodesForOperator is returned when no nodes are available for the client's operator.
	ErrNoNodesForOperator = errors.New("no nodes available for the client's operator")
)

// IPDBQuerier 定义了 IP 查询接口，便于测试时模拟
type IPDBQuerier interface {
	Query(ip string) (int, error)
}

// DefaultIPDBQuerier 使用默认的 ipdb 包实现 IP 查询
type DefaultIPDBQuerier struct{}

// Query 实现 IPDBQuerier 接口，调用 ipdb 包的 Query 函数
func (q DefaultIPDBQuerier) Query(ip string) (int, error) {
	return ipdb.Query(ip)
}

// EtcdServiceInterface 定义了 EtcdService 的接口，便于测试时模拟
type EtcdServiceInterface interface {
	LoadAllNodes() ([]models.Node, error)
	LoadAllAppSecretKeys() ([]models.AppSecrets, error)
	RegisterNodeCallback(callback NodeChangeCallback)
	RegisterAppSecretCallback(callback AppSecretEventCallback)
	Close()
}

// 节点评分权重常量
const (
	diskUsageWeight       = 0.3 // 磁盘使用率权重 (越低越好)
	diskFreeWeight        = 0.3 // 磁盘剩余空间权重 (越高越好)
	concurrentTasksWeight = 0.4 // 并发任务数权重 (越少越好)

	MAX_REASONABLE_FREE_DISK_GB = 500.0 // 用于归一化磁盘剩余空间得分的最大合理空闲GB数
)

// SchedulerService 提供节点调度服务
type SchedulerService struct {
	operatorNodes map[int]map[string]models.Node // Grouped by OperatorID
	mu            sync.RWMutex
	etcdService   EtcdServiceInterface           // 使用接口而非具体实现
	ipdbQuerier   IPDBQuerier                    // IP 查询接口
	nodeMetrics   map[string]models.NodeMetrics  // 存储节点的最新动态指标
	metricsMu     sync.RWMutex                   // 保护 nodeMetrics 的读写
	appSecrets    map[string]models.AppSecrets   // 存储应用密钥
	secretsMu     sync.RWMutex                   // 保护 appSecrets 的读写
}

// NewSchedulerService 创建一个新的调度服务
// Accepts Etcd configuration
func NewSchedulerService(etcdCfg configs.EtcdConfig) *SchedulerService {
	return NewSchedulerServiceWithDeps(etcdCfg, nil, DefaultIPDBQuerier{})
}

// NewSchedulerServiceWithDeps 创建一个新的调度服务，允许注入依赖
// 用于测试时注入模拟依赖
func NewSchedulerServiceWithDeps(etcdCfg configs.EtcdConfig, etcdService EtcdServiceInterface, ipdbQuerier IPDBQuerier) *SchedulerService {
	// 创建调度服务
	scheduler := &SchedulerService{
		operatorNodes: make(map[int]map[string]models.Node),
		nodeMetrics:   make(map[string]models.NodeMetrics), // 初始化 metrics map
		appSecrets:    make(map[string]models.AppSecrets),
		ipdbQuerier:   ipdbQuerier,
	}

	// Initialize maps for each known operator type to avoid nil map writes later
	scheduler.operatorNodes[ipdb.Unicom] = make(map[string]models.Node)
	scheduler.operatorNodes[ipdb.Mobile] = make(map[string]models.Node)
	scheduler.operatorNodes[ipdb.Telecom] = make(map[string]models.Node)
	scheduler.operatorNodes[ipdb.UnknownOperator] = make(map[string]models.Node)

	// 添加硬编码的测试应用密钥
	scheduler.appSecrets["test-app-123"] = models.AppSecrets{
		AppID:  "test-app-123",
		Secret: "app-secret-key",
	}
	zap.S().Info("已添加硬编码的测试应用密钥: test-app-123")

	// 如果没有提供 etcdService，则创建一个
	if etcdService == nil {
		var err error
		etcdService, err = NewEtcdService(etcdCfg.Endpoints) // Use endpoints from config
		if err != nil {
			zap.S().Warnf("创建etcd服务失败: %v，将使用内存中的默认节点数据", err)

			// 如果连接etcd失败，使用默认节点数据 (Consider removing this if config is mandatory)
			defaultNodes := []models.Node{
				{ID: "mem-1", IP: "127.0.0.1", Port: 8080, Capacity: 100},
				{ID: "mem-2", IP: "127.0.0.1", Port: 8081, Capacity: 100},
			}

			// 将默认节点添加到map中
			for _, node := range defaultNodes {
				ipStr := node.IP // Directly use node.IP
				operatorID, queryErr := scheduler.ipdbQuerier.Query(ipStr)
				if queryErr != nil {
					zap.S().Errorf("Failed to query operator for default node %s IP %s: %v", node.ID, ipStr, queryErr)
					operatorID = ipdb.UnknownOperator // Default to unknown on error
				}
				scheduler.operatorNodes[operatorID][node.ID] = node
			}

			return scheduler // Return scheduler with in-memory nodes
		}
	}

	scheduler.etcdService = etcdService

	// 从etcd加载所有节点
	initialNodes, err := etcdService.LoadAllNodes()
	if err != nil {
		zap.S().Warnf("从etcd加载初始节点失败: %v", err)
		// Depending on policy, might want to handle this more gracefully or fail startup
	} else {
		zap.S().Infof("从etcd成功加载 %d 个初始节点", len(initialNodes))
		for _, node := range initialNodes {
			ipStr := node.IP // Directly use node.IP
			operatorID, queryErr := scheduler.ipdbQuerier.Query(ipStr)
			if queryErr != nil {
				zap.S().Errorf("Failed to query operator for etcd node %s IP %s: %v", node.ID, ipStr, queryErr)
				operatorID = ipdb.UnknownOperator // Default to unknown on error
			}
			scheduler.operatorNodes[operatorID][node.ID] = node
		}
	}

	// 从etcd加载所有应用密钥
	initialAppSecrets, err := etcdService.LoadAllAppSecretKeys()
	if err != nil {
		zap.S().Warnf("从etcd加载初始应用密钥失败: %v", err)
		// Depending on policy, might want to handle this more gracefully or fail startup
	} else {
		zap.S().Infof("从etcd成功加载 %d 个初始应用密钥", len(initialAppSecrets))
		for _, appSecret := range initialAppSecrets {
			scheduler.appSecrets[appSecret.AppID] = appSecret
		}
	}

	// 注册回调函数来处理节点变更
	scheduler.etcdService.RegisterNodeCallback(scheduler.handleNodeChange)

	// 注册回调函数来处理应用密钥变更
	scheduler.etcdService.RegisterAppSecretCallback(scheduler.handleAppSecretEvent)

	return scheduler
}

// SelectNode 根据负载均衡策略选择一个可用节点
// 1. 根据客户端IP确定运营商
// 2. 从该运营商的所有已注册节点中选择
// 3. 如果找不到客户端IP所在运营商，则从所有节点中随机选择
// 4. 对节点进行评分，综合考虑磁盘使用率 (低的好) 和并发任务数 (低的好)
func (s *SchedulerService) SelectNode(ipStr string) (models.Node, error) {
	return s.selectNodeWithQuerier(ipStr, s.ipdbQuerier)
}

// selectNodeWithQuerier 是 SelectNode 的内部实现，接受一个 IPDBQuerier 参数
// 这使得测试时可以注入模拟的 IP 查询器
func (s *SchedulerService) selectNodeWithQuerier(ipStr string, querier IPDBQuerier) (models.Node, error) {
	// 使用读锁保护对 operatorNodes 的访问
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 1. 根据IP获取运营商ID
	operatorID, err := querier.Query(ipStr)
	if err != nil {
		zap.S().Errorf("Failed to query operator for client IP %s: %v", ipStr, err)
		return models.Node{}, ErrNoOperatorFound
	}
	zap.S().Infof("Client IP %s maps to OperatorID: %d", ipStr, operatorID)

	// 2. 获取该运营商的节点列表
	nodesForOperator, ok := s.operatorNodes[operatorID]
	
	// 如果找不到该运营商的节点或者运营商是 Unknown，从所有节点中随机选择
	if !ok || len(nodesForOperator) == 0 || operatorID == ipdb.UnknownOperator {
		return s.selectRandomNodeFromAll(ipStr)
	}

	// 3. 使用该运营商的所有已注册节点
	nodes := s.mapToSlice(nodesForOperator)

	// 4. 如果只有一个节点，直接返回
	if len(nodes) == 1 {
		zap.S().Infof("Only one node available, selecting node ID: %s", nodes[0].ID)
		return nodes[0], nil
	}

	// 5. 对节点进行评分，选择最优节点
	return s.selectBestNodeByScore(nodes, ipStr)
}

// selectRandomNodeFromAll 从所有节点中随机选择一个节点
func (s *SchedulerService) selectRandomNodeFromAll(ipStr string) (models.Node, error) {
	// 初始化随机数生成器
	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	
	zap.S().Warnf("Selecting from all nodes for client IP: %s", ipStr)
	
	// 收集所有节点
	allNodes := s.getAllNodes()
	
	// 如果没有节点，返回错误
	if len(allNodes) == 0 {
		return models.Node{}, ErrNoNodes
	}
	
	// 随机选择一个节点
	randomIndex := rnd.Intn(len(allNodes))
	selectedNode := allNodes[randomIndex]
	zap.S().Infof("Randomly selected node %s for client IP %s from all nodes", selectedNode.ID, ipStr)
	return selectedNode, nil
}

// getAllNodes 获取所有节点列表
func (s *SchedulerService) getAllNodes() []models.Node {
	allNodes := make([]models.Node, 0)
	for _, operatorNodes := range s.operatorNodes {
		for _, node := range operatorNodes {
			allNodes = append(allNodes, node)
		}
	}
	return allNodes
}

// mapToSlice 将节点映射转换为切片
func (s *SchedulerService) mapToSlice(nodeMap map[string]models.Node) []models.Node {
	nodes := make([]models.Node, 0, len(nodeMap))
	for _, node := range nodeMap {
		nodes = append(nodes, node)
	}
	return nodes
}

// selectBestNodeByScore 根据节点评分选择最佳节点
func (s *SchedulerService) selectBestNodeByScore(nodes []models.Node, ipStr string) (models.Node, error) {
	// 使用读锁保护对 nodeMetrics 的访问
	s.metricsMu.RLock()
	defer s.metricsMu.RUnlock()

	var bestNode models.Node
	bestScore := -1.0 // 初始化一个足够低的分数

	for _, node := range nodes {
		score := s.calculateNodeScore(node)
		if score > bestScore {
			bestScore = score
			bestNode = node
		}
	}

	// 如果没有找到最佳节点 (所有节点都没有指标数据)，返回第一个节点
	if bestScore == -1.0 {
		zap.S().Warnf("Could not score any nodes, selecting first node: %s", nodes[0].ID)
		return nodes[0], nil
	}

	zap.S().Infof("Selected node %s with best score %.2f for client IP %s", bestNode.ID, bestScore, ipStr)
	return bestNode, nil
}

// calculateNodeScore 计算节点的评分
// 返回节点的评分，如果没有指标数据则返回 -1
func (s *SchedulerService) calculateNodeScore(node models.Node) float64 {
	// 获取节点的动态指标
	s.metricsMu.RLock() // Ensure read lock for metrics
	metrics, ok := s.nodeMetrics[node.ID]
	s.metricsMu.RUnlock()

	if !ok {
		// 如果没有指标数据，返回 -1
		zap.S().Warnf("No metrics data for node %s, skipping in scoring", node.ID)
		return -1.0
	}

	// 计算磁盘使用率得分 (使用率越低得分越高, metrics.DiskUsage is 0.0 to 1.0)
	diskScore := 1.0 - metrics.DiskUsage
	if diskScore < 0 { // Ensure score is not negative if DiskUsage > 1.0 somehow
		diskScore = 0
	}

	// 计算磁盘剩余空间得分 (剩余空间越多得分越高, 上限为1.0)
	// metrics.DiskFree is in GB
	diskFreeScore := 0.0
	if MAX_REASONABLE_FREE_DISK_GB > 0 {
		diskFreeScore = math.Min(1.0, metrics.DiskFree/MAX_REASONABLE_FREE_DISK_GB)
	}
	if metrics.DiskFree < 0 { // Penalize if data is inconsistent
		diskFreeScore = 0
	}

	// 计算并发任务数得分 (任务越少得分越高)
	// Add 1 to denominator to prevent division by zero and handle ConcurrentTasks = 0 correctly.
	concurrentScore := 1.0 / (1.0 + float64(metrics.ConcurrentTasks))                 

	// 计算加权总分
	totalScore := (diskUsageWeight * diskScore) +
		(diskFreeWeight * diskFreeScore) +
		(concurrentTasksWeight * concurrentScore)

	zap.S().Debugf("Node %s score: diskUsage=%.2f (raw %f), diskFree=%.2f (raw %.2fGB), concurrent=%.2f (raw %d), total=%.2f",
		node.ID, diskScore, metrics.DiskUsage, diskFreeScore, metrics.DiskFree, concurrentScore, metrics.ConcurrentTasks, totalScore)

	return totalScore
}

// GetAllNodes 获取所有节点
func (s *SchedulerService) GetAllNodes() []models.Node {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 将map转换为slice
	var nodesList []models.Node
	for _, nodes := range s.operatorNodes {
		for _, node := range nodes {
			nodesList = append(nodesList, node)
		}
	}
	
	return nodesList
}

// handleNodeChange 处理节点变更事件
func (s *SchedulerService) handleNodeChange(node models.Node, action string) {
	s.handleNodeChangeWithQuerier(node, action, s.ipdbQuerier)
}

// handleNodeChangeWithQuerier 是 handleNodeChange 的内部实现，接受一个 IPDBQuerier 参数
// 这使得测试时可以注入模拟的 IP 查询器
func (s *SchedulerService) handleNodeChangeWithQuerier(node models.Node, action string, querier IPDBQuerier) {
	s.mu.Lock()
	defer s.mu.Unlock()

	zap.S().Infof("处理节点变更事件: 节点=%s, 操作=%s", node.ID, action)

	switch action {
	case "set", "create", "update":
		// 对于添加或更新操作，需要确定运营商 ID
		ipStr := node.IP
		operatorID, queryErr := querier.Query(ipStr)
		if queryErr != nil {
			zap.S().Errorf("Failed to query operator for node %s IP %s in ManageNode: %v", node.ID, ipStr, queryErr)
			operatorID = ipdb.UnknownOperator
		}

		// 确保运营商对应的 map 存在
		if _, ok := s.operatorNodes[operatorID]; !ok {
			s.operatorNodes[operatorID] = make(map[string]models.Node)
		}
		s.operatorNodes[operatorID][node.ID] = node
		zap.S().Infof("节点 %s (运营商: %d) 已添加或更新", node.ID, operatorID)

	case "delete", "expire":
		// 对于删除操作，需要遍历所有运营商的节点列表，找到并删除该节点
		nodeFound := false
		
		// 遍历所有运营商
		for operatorID, nodeList := range s.operatorNodes {
			// 检查该运营商的节点列表中是否存在要删除的节点
			if _, exists := nodeList[node.ID]; exists {
				delete(nodeList, node.ID)
				zap.S().Infof("节点 %s (运营商: %d) 已删除", node.ID, operatorID)
				nodeFound = true
				break // 找到并删除后就可以退出循环
			}
		}
		
		// 如果没有找到要删除的节点，记录警告日志
		if !nodeFound {
			zap.S().Warnf("尝试删除节点 %s 失败：节点不存在于任何运营商的列表中", node.ID)
		}
		
	default:
		zap.S().Warnf("收到未处理的节点事件类型: %s, 节点ID: %s", action, node.ID)
	}
}

// handleAppSecretEvent 处理来自 etcd 的 App Secret 变更事件
func (s *SchedulerService) handleAppSecretEvent(key string, value string, action string) {
	zap.S().Debugf("Handling app secret event: Action=%s, Key=%s", action, key)

	// Extract AppID from the key (e.g., /app_secrets/app123 -> app123)
	if !strings.HasPrefix(key, "/app_secrets/") {
		zap.S().Warnf("Received app secret event with unexpected key prefix: %s", key)
		return
	}
	appId := strings.TrimPrefix(key, "/app_secrets/")
	if appId == "" {
		zap.S().Warnf("Could not extract AppID from key: %s", key)
		return
	}

	s.secretsMu.Lock() // Lock for writing
	defer s.secretsMu.Unlock()

	switch action {
	case "set", "create", "update":
		var appSecret models.AppSecrets
		if err := json.Unmarshal([]byte(value), &appSecret); err != nil {
			zap.S().Errorf("Failed to unmarshal app secret data for key %s: %v. Value: %s", key, err, value)
			return // Skip if data is invalid
		}
		// Validate if the AppID in the data matches the key's AppID
		if appSecret.AppID != appId {
			zap.S().Warnf("AppID mismatch in app secret event: Key AppID='%s', Data AppID='%s'", appId, appSecret.AppID)
			// Decide how to handle mismatch: ignore, log error, use key's AppID?
			// Using key's AppID might be safer if the data is potentially corrupt.
			// For now, let's update based on the key derived AppID but log the warning.
			// Or, perhaps we should trust the data's AppID if present?
			// Let's trust the data's AppID if present and valid, otherwise use key's.
			idToUse := appSecret.AppID
			if idToUse == "" { // If AppID in value is empty, trust the key
				idToUse = appId
				appSecret.AppID = appId // Ensure the struct has the ID
				zap.S().Debugf("AppID empty in secret value for key %s, using AppID derived from key: %s", key, appId)
			} else if idToUse != appId {
				// If they mismatch, log warning but proceed with AppID from data value
				zap.S().Warnf("AppID mismatch for key %s. Using AppID from value: %s", key, idToUse)
			}

			// Update or add the secret using the determined ID
			s.appSecrets[idToUse] = appSecret
			zap.S().Infof("App secret updated/added via etcd watch: AppID=%s", idToUse)

		} else {
			// AppID matches or was derived from key
			s.appSecrets[appId] = appSecret
			zap.S().Infof("App secret updated/added via etcd watch: AppID=%s", appId)
		}

	case "delete", "expire":
		// Check if the secret exists before deleting
		if _, exists := s.appSecrets[appId]; exists {
			delete(s.appSecrets, appId)
			zap.S().Infof("App secret deleted via etcd watch: AppID=%s", appId)
		} else {
			zap.S().Warnf("Received delete event for non-existent AppID: %s", appId)
		}

	default:
		zap.S().Warnf("Unhandled action in app secret event handler: %s for key %s", action, key)
	}

	// Optional: Log the current state of secrets after change (for debugging)
	// zap.S().Debugf("Current app secrets map size: %d", len(s.appSecrets))
}

// UpdateNodeMetrics 更新节点的动态指标
func (s *SchedulerService) UpdateNodeMetrics(nodeID string, metrics models.NodeMetrics) error {
	s.metricsMu.Lock()
	defer s.metricsMu.Unlock()

	// 可选: 验证 nodeID 是否存在于 s.nodes (需要加读锁)
	s.mu.RLock()
	found := false
	for _, nodes := range s.operatorNodes {
		if _, exists := nodes[nodeID]; exists {
			found = true
			break
		}
	}
	s.mu.RUnlock()
	if !found {
		zap.S().Warnf("尝试更新未知节点 %s 的指标", nodeID)
		// 根据策略决定是否报错。这里暂时不报错，允许上报孤立节点的指标
		// return errors.New("尝试更新未知节点的指标")
	}

	metrics.UpdatedAt = time.Now() // 记录更新时间
	s.nodeMetrics[nodeID] = metrics
	zap.S().Infof("已更新节点 %s 的指标: %+v", nodeID, metrics)
	return nil
}

func (s *SchedulerService) AuthAppToken(appId string, token string, ts string) (bool, error) {
	s.secretsMu.RLock()
	appSecret, exists := s.appSecrets[appId]
	s.secretsMu.RUnlock()
	if !exists {
		return false, errors.New("app id not found")
	}

	content := fmt.Sprintf("%s|%s|%s", appSecret.Secret, appId, ts)
	md5Res := md5.Sum([]byte(content))
	encryptedContent := hex.EncodeToString(md5Res[:])
	if encryptedContent == strings.ToLower(token) {
		return true, nil
	}
	return false, errors.New("invalid token")
}

// Close 关闭调度服务
func (s *SchedulerService) Close() {
	// 如果etcd服务可用，关闭它
	if s.etcdService != nil {
		s.etcdService.Close()
	}
}
