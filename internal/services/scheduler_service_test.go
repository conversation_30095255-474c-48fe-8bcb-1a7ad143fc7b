package services

import (
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"sync"
	"testing"

	"funshion.com/upload_tuner/configs"
	"funshion.com/upload_tuner/internal/models"
	"funshion.com/upload_tuner/ipdb"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// 测试用错误常量
var (
	ErrNoAvailableNode = errors.New("no nodes available")
	ErrInvalidToken    = errors.New("invalid token")
	ErrAppNotFound     = errors.New("app id not found")
)

// mockEtcdService 模拟 EtcdServiceInterface 接口
type mockEtcdService struct {
	mock.Mock
	nodeCallbacks      []NodeChangeCallback
	appSecretCallbacks []AppSecretEventCallback
}

func (m *mockEtcdService) LoadAllNodes() ([]models.Node, error) {
	args := m.Called()
	return args.Get(0).([]models.Node), args.Error(1)
}

func (m *mockEtcdService) LoadAllAppSecretKeys() ([]models.AppSecrets, error) {
	args := m.Called()
	return args.Get(0).([]models.AppSecrets), args.Error(1)
}

func (m *mockEtcdService) RegisterNodeCallback(callback NodeChangeCallback) {
	m.Called(callback)
	m.nodeCallbacks = append(m.nodeCallbacks, callback)
}

func (m *mockEtcdService) RegisterAppSecretCallback(callback AppSecretEventCallback) {
	m.Called(callback)
	m.appSecretCallbacks = append(m.appSecretCallbacks, callback)
}

func (m *mockEtcdService) Close() {
	m.Called()
}

// mockIPDBQuerier 模拟 IPDBQuerier 接口
type mockIPDBQuerier struct {
	returnOperatorID int
	returnErr        error
}

func (m mockIPDBQuerier) Query(ip string) (int, error) {
	return m.returnOperatorID, m.returnErr
}

// TestNewSchedulerServiceWithDeps 测试创建调度器服务的功能
func TestNewSchedulerServiceWithDeps(t *testing.T) {
	tests := []struct {
		name            string
		etcdCfg         configs.EtcdConfig
		mockEtcdSetup   func(*mockEtcdService)
		ipdbQuerier     IPDBQuerier
		expectedNodeLen int
	}{
		{
			name:    "成功创建调度服务",
			etcdCfg: configs.EtcdConfig{Endpoints: []string{"http://localhost:2379"}},
			mockEtcdSetup: func(mockEtcd *mockEtcdService) {
				nodes := []models.Node{
					{ID: "node1", IP: "***********", Port: 8080, Capacity: 100},
					{ID: "node2", IP: "***********", Port: 8080, Capacity: 200},
				}
				appSecrets := []models.AppSecrets{
					{AppID: "app1", Secret: "secret1"},
					{AppID: "app2", Secret: "secret2"},
				}
				mockEtcd.On("LoadAllNodes").Return(nodes, nil)
				mockEtcd.On("LoadAllAppSecretKeys").Return(appSecrets, nil)
				mockEtcd.On("RegisterNodeCallback", mock.Anything).Return()
				mockEtcd.On("RegisterAppSecretCallback", mock.Anything).Return()
			},
			ipdbQuerier:     mockIPDBQuerier{returnOperatorID: ipdb.Unicom, returnErr: nil},
			expectedNodeLen: 2,
		},
		{
			name:    "创建调度服务但加载节点失败",
			etcdCfg: configs.EtcdConfig{Endpoints: []string{"http://localhost:2379"}},
			mockEtcdSetup: func(mockEtcd *mockEtcdService) {
				mockEtcd.On("LoadAllNodes").Return([]models.Node{}, errors.New("加载节点失败"))
				mockEtcd.On("LoadAllAppSecretKeys").Return([]models.AppSecrets{}, nil)
				mockEtcd.On("RegisterNodeCallback", mock.Anything).Return()
				mockEtcd.On("RegisterAppSecretCallback", mock.Anything).Return()
			},
			ipdbQuerier:     mockIPDBQuerier{returnOperatorID: ipdb.Unicom, returnErr: nil},
			expectedNodeLen: 0,
		},
		{
			name:    "创建调度服务但加载应用密钥失败",
			etcdCfg: configs.EtcdConfig{Endpoints: []string{"http://localhost:2379"}},
			mockEtcdSetup: func(mockEtcd *mockEtcdService) {
				mockEtcd.On("LoadAllNodes").Return([]models.Node{}, nil)
				mockEtcd.On("LoadAllAppSecretKeys").Return([]models.AppSecrets{}, errors.New("加载应用密钥失败"))
				mockEtcd.On("RegisterNodeCallback", mock.Anything).Return()
				mockEtcd.On("RegisterAppSecretCallback", mock.Anything).Return()
			},
			ipdbQuerier:     mockIPDBQuerier{returnOperatorID: ipdb.Unicom, returnErr: nil},
			expectedNodeLen: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模拟的 EtcdService
			mockEtcd := new(mockEtcdService)
			mockEtcd.nodeCallbacks = make([]NodeChangeCallback, 0)
			mockEtcd.appSecretCallbacks = make([]AppSecretEventCallback, 0)

			// 设置模拟行为
			if tt.mockEtcdSetup != nil {
				tt.mockEtcdSetup(mockEtcd)
			}

			// 调用被测试的方法
			scheduler := NewSchedulerServiceWithDeps(tt.etcdCfg, mockEtcd, tt.ipdbQuerier)

			// 验证结果
			assert.NotNil(t, scheduler)
			assert.NotNil(t, scheduler.operatorNodes)
			assert.NotNil(t, scheduler.nodeMetrics)
			assert.NotNil(t, scheduler.appSecrets)

			// 验证模拟对象的调用
			mockEtcd.AssertExpectations(t)
			
			// 验证节点数量
			nodes := scheduler.GetAllNodes()
			assert.Len(t, nodes, tt.expectedNodeLen)
		})
	}
}

// TestSelectNode 测试节点选择功能
func TestSelectNode(t *testing.T) {
	tests := []struct {
		name          string
		clientIP      string
		operatorNodes map[int]map[string]models.Node
		nodeMetrics   map[string]models.NodeMetrics
		ipdbQuerier   IPDBQuerier
		expectedNode  string
		wantErr       bool
		errType       error
	}{
		{
			name:     "成功选择运营商节点",
			clientIP: "***********00",
			operatorNodes: map[int]map[string]models.Node{
				ipdb.Unicom: {
					"node1": {ID: "node1", IP: "***********", Port: 8080, Capacity: 100},
					"node2": {ID: "node2", IP: "***********", Port: 8080, Capacity: 200},
				},
				ipdb.Mobile: {
					"node3": {ID: "node3", IP: "***********", Port: 8080, Capacity: 100},
				},
			},
			nodeMetrics: map[string]models.NodeMetrics{
				"node1": {DiskUsage: 0.3, ConcurrentTasks: 10},
				"node2": {DiskUsage: 0.2, ConcurrentTasks: 5},
			},
			ipdbQuerier:  mockIPDBQuerier{returnOperatorID: ipdb.Unicom, returnErr: nil},
			expectedNode: "node2", // node2 有更好的评分
			wantErr:      false,
			errType:      nil,
		},
		{
			name:     "查询运营商失败",
			clientIP: "***********00",
			operatorNodes: map[int]map[string]models.Node{
				ipdb.Unicom: {
					"node1": {ID: "node1", IP: "***********", Port: 8080, Capacity: 100},
				},
			},
			ipdbQuerier:  mockIPDBQuerier{returnOperatorID: 0, returnErr: errors.New("查询运营商失败")},
			expectedNode: "",
			wantErr:      true,
			errType:      ErrNoOperatorFound,
		},
		{
			name:     "运营商没有节点，从所有节点中选择",
			clientIP: "***********00",
			operatorNodes: map[int]map[string]models.Node{
				ipdb.Unicom: {},
				ipdb.Mobile: {
					"node3": {ID: "node3", IP: "***********", Port: 8080, Capacity: 100},
				},
			},
			ipdbQuerier:  mockIPDBQuerier{returnOperatorID: ipdb.Unicom, returnErr: nil},
			expectedNode: "node3", // 从所有节点中随机选择
			wantErr:      false,
			errType:      nil,
		},
		{
			name:     "没有可用节点",
			clientIP: "***********00",
			operatorNodes: map[int]map[string]models.Node{
				ipdb.Unicom: {},
				ipdb.Mobile: {},
			},
			ipdbQuerier:  mockIPDBQuerier{returnOperatorID: ipdb.Unicom, returnErr: nil},
			expectedNode: "",
			wantErr:      true,
			errType:      ErrNoNodes,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建调度服务
			scheduler := &SchedulerService{
				operatorNodes: tt.operatorNodes,
				nodeMetrics:   tt.nodeMetrics,
				mu:           sync.RWMutex{},
				ipdbQuerier:  tt.ipdbQuerier,
			}

			// 调用被测试的方法
			node, err := scheduler.selectNodeWithQuerier(tt.clientIP, tt.ipdbQuerier)

			// 验证结果
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errType != nil {
					assert.Equal(t, tt.errType, err)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedNode, node.ID)
			}
		})
	}
}

// TestUpdateNodeMetrics 测试节点指标更新功能
func TestUpdateNodeMetrics(t *testing.T) {
	tests := []struct {
		name       string
		nodeID     string
		metrics    models.NodeMetrics
		initNodes  map[string]models.NodeMetrics
		expectErr  bool
		expectSize int
	}{
		{
			name:   "成功更新现有节点指标",
			nodeID: "node1",
			metrics: models.NodeMetrics{
				DiskUsage:       0.5,
				ConcurrentTasks: 20,
			},
			initNodes: map[string]models.NodeMetrics{
				"node1": {DiskUsage: 0.3, ConcurrentTasks: 10},
				"node2": {DiskUsage: 0.2, ConcurrentTasks: 5},
			},
			expectErr:  false,
			expectSize: 2,
		},
		{
			name:   "添加新节点指标",
			nodeID: "node3",
			metrics: models.NodeMetrics{
				DiskUsage:       0.1,
				ConcurrentTasks: 3,
			},
			initNodes: map[string]models.NodeMetrics{
				"node1": {DiskUsage: 0.3, ConcurrentTasks: 10},
				"node2": {DiskUsage: 0.2, ConcurrentTasks: 5},
			},
			expectErr:  false,
			expectSize: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建调度服务
			scheduler := &SchedulerService{
				nodeMetrics: make(map[string]models.NodeMetrics),
				operatorNodes: map[int]map[string]models.Node{
					ipdb.Unicom: {
						"node1": {ID: "node1", IP: "***********", Port: 8080, Capacity: 100},
						"node2": {ID: "node2", IP: "***********", Port: 8080, Capacity: 200},
					},
				},
				mu:       sync.RWMutex{},
				metricsMu: sync.RWMutex{},
			}
			
			// 初始化指标数据
			for id, metrics := range tt.initNodes {
				scheduler.nodeMetrics[id] = metrics
			}

			// 调用被测试的方法
			err := scheduler.UpdateNodeMetrics(tt.nodeID, tt.metrics)

			// 验证结果
			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Len(t, scheduler.nodeMetrics, tt.expectSize)
				
				// 验证指标字段，不比较 UpdatedAt 字段
				actualMetrics := scheduler.nodeMetrics[tt.nodeID]
				assert.Equal(t, tt.metrics.DiskUsage, actualMetrics.DiskUsage)
				assert.Equal(t, tt.metrics.ConcurrentTasks, actualMetrics.ConcurrentTasks)
				
				// 验证 UpdatedAt 字段不为零值
				assert.False(t, actualMetrics.UpdatedAt.IsZero())
			}
		})
	}
}

// TestAuthAppToken 测试应用令牌验证功能
func TestAuthAppToken(t *testing.T) {
	// 准备测试数据
	appID := "testApp"
	appSecret := "testSecret"
	timestamp := "1609459200" // 2021-01-01 00:00:00
	nonce := "randomNonce"

	// 计算正确的签名
	content := fmt.Sprintf("%s|%s|%s", appSecret, appID, timestamp)
	md5Res := md5.Sum([]byte(content))
	token := hex.EncodeToString(md5Res[:])
	correctToken := appID + ":" + timestamp + ":" + nonce + ":" + token

	tests := []struct {
		name       string
		token      string
		appSecrets map[string]string
		wantErr    bool
		errType    error
	}{
		{
			name:  "成功验证令牌",
			token: correctToken,
			appSecrets: map[string]string{
				appID: appSecret,
			},
			wantErr: false,
			errType: nil,
		},
		{
			name:  "令牌格式错误",
			token: appID + ":" + timestamp + ":" + nonce + ":wrongtoken",
			appSecrets: map[string]string{
				appID: appSecret,
			},
			wantErr: true,
			errType: ErrInvalidToken,
		},
		{
			name:  "应用不存在",
			token: correctToken,
			appSecrets: map[string]string{
				"otherApp": appSecret,
			},
			wantErr: true,
			errType: ErrAppNotFound,
		},
		{
			name:       "令牌验证失败",
			token:      appID + ":" + timestamp + ":" + nonce + ":wrongSignature",
			appSecrets: map[string]string{
				appID: appSecret,
			},
			wantErr: true,
			errType: ErrInvalidToken,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建调度服务
			scheduler := &SchedulerService{
				appSecrets: make(map[string]models.AppSecrets),
				mu:         sync.RWMutex{},
				secretsMu:  sync.RWMutex{},
			}

			// 设置应用密钥
			for id, secret := range tt.appSecrets {
				scheduler.appSecrets[id] = models.AppSecrets{AppID: id, Secret: secret}
			}

			// 对于有效的令牌格式，从令牌中解析参数
			var appID, ts, token string
			
			// 如果是标准格式的令牌，则解析出参数
			parts := strings.Split(tt.token, ":")
			if len(parts) >= 4 {
				appID = parts[0]
				ts = parts[1]
				// nonce 不需要传递给 AuthAppToken
				_ = parts[2] // nonce
				token = parts[3]
			} else {
				// 对于无效格式的令牌，使用测试数据中的 appID 和空的 token
				for id := range tt.appSecrets {
					appID = id
					break
				}
				ts = timestamp
				token = tt.token // 直接使用原始令牌作为 token
			}
			
			// 调用被测试的方法
			valid, err := scheduler.AuthAppToken(appID, token, ts)

			// 验证结果
			if tt.wantErr {
				assert.False(t, valid)
				assert.Error(t, err)
				if tt.errType != nil {
					assert.Equal(t, tt.errType.Error(), err.Error())
				}
			} else {
				assert.True(t, valid)
				assert.NoError(t, err)
			}
		})
	}
}

// TestHandleAppSecretEvent 测试应用密钥事件处理功能
func TestHandleAppSecretEvent(t *testing.T) {
	tests := []struct {
		name          string
		key           string
		value         string
		action        string
		initialSecrets map[string]models.AppSecrets
		expectedLen   int
		expectedValue string
	}{
		{
			name:   "添加新应用密钥",
			key:    "/app_secrets/app1",
			value:  `{"AppID":"app1","Secret":"secret1"}`,
			action: "set",
			initialSecrets: map[string]models.AppSecrets{},
			expectedLen:   1,
			expectedValue: "secret1",
		},
		{
			name:   "更新现有应用密钥",
			key:    "/app_secrets/app1",
			value:  `{"AppID":"app1","Secret":"newSecret"}`,
			action: "set",
			initialSecrets: map[string]models.AppSecrets{
				"app1": {AppID: "app1", Secret: "oldSecret"},
			},
			expectedLen:   1,
			expectedValue: "newSecret",
		},
		{
			name:   "删除应用密钥",
			key:    "/app_secrets/app1",
			value:  "",
			action: "delete",
			initialSecrets: map[string]models.AppSecrets{
				"app1": {AppID: "app1", Secret: "secret1"},
			},
			expectedLen:   0,
			expectedValue: "",
		},
		{
			name:   "未知操作",
			key:    "/app_secrets/app1",
			value:  `{"AppID":"app1","Secret":"secret1"}`,
			action: "unknown",
			initialSecrets: map[string]models.AppSecrets{},
			expectedLen:   0,
			expectedValue: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建调度服务
			scheduler := &SchedulerService{
				appSecrets: make(map[string]models.AppSecrets),
				mu:        sync.RWMutex{},
				secretsMu: sync.RWMutex{},
			}
			
			// 设置初始密钥
			for id, secret := range tt.initialSecrets {
				scheduler.appSecrets[id] = secret
			}

			// 调用被测试的方法
			scheduler.handleAppSecretEvent(tt.key, tt.value, tt.action)

			// 验证结果
			assert.Len(t, scheduler.appSecrets, tt.expectedLen)
			if tt.action == "set" {
				appID := strings.TrimPrefix(tt.key, "/app_secrets/")
				if tt.expectedLen > 0 {
					assert.Contains(t, scheduler.appSecrets, appID)
					assert.Equal(t, tt.expectedValue, scheduler.appSecrets[appID].Secret)
				}
			} else if tt.action == "delete" {
				appID := strings.TrimPrefix(tt.key, "/app_secrets/")
				_, exists := scheduler.appSecrets[appID]
				assert.False(t, exists)
			}
		})
	}
}

// TestGetAllNodes 测试获取所有节点功能
func TestGetAllNodes(t *testing.T) {
	tests := []struct {
		name          string
		operatorNodes map[int]map[string]models.Node
		expectedLen   int
	}{
		{
			name: "获取所有节点",
			operatorNodes: map[int]map[string]models.Node{
				ipdb.Unicom: {
					"node1": {ID: "node1", IP: "***********", Port: 8080, Capacity: 100},
					"node2": {ID: "node2", IP: "***********", Port: 8080, Capacity: 200},
				},
				ipdb.Mobile: {
					"node3": {ID: "node3", IP: "***********", Port: 8080, Capacity: 100},
				},
			},
			expectedLen: 3,
		},
		{
			name: "没有节点",
			operatorNodes: map[int]map[string]models.Node{
				ipdb.Unicom: {},
				ipdb.Mobile: {},
			},
			expectedLen: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建调度服务
			scheduler := &SchedulerService{
				operatorNodes: tt.operatorNodes,
				mu:            sync.RWMutex{},
			}

			// 调用被测试的方法
			nodes := scheduler.GetAllNodes()

			// 验证结果
			assert.Len(t, nodes, tt.expectedLen)
		})
	}
}
