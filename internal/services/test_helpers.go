package services

import (
	"context"
	
	"github.com/stretchr/testify/mock"
	"funshion.com/upload_tuner/internal/models"
	"go.etcd.io/etcd/client/v2"
)

// 模拟 EtcdService 接口
type MockEtcdService struct {
	mock.Mock
	nodeCallbacks      []NodeChangeCallback
	appSecretCallbacks []AppSecretEventCallback
}

func (m *MockEtcdService) LoadAllNodes() ([]models.Node, error) {
	args := m.Called()
	return args.Get(0).([]models.Node), args.Error(1)
}

func (m *MockEtcdService) LoadAllAppSecretKeys() ([]models.AppSecrets, error) {
	args := m.Called()
	return args.Get(0).([]models.AppSecrets), args.Error(1)
}

func (m *MockEtcdService) RegisterNodeCallback(callback NodeChangeCallback) {
	m.Called(callback)
	m.nodeCallbacks = append(m.nodeCallbacks, callback)
}

func (m *MockEtcdService) RegisterAppSecretCallback(callback AppSecretEventCallback) {
	m.Called(callback)
	m.appSecretCallbacks = append(m.appSecretCallbacks, callback)
}

func (m *MockEtcdService) Close() {
	m.Called()
}

// 模拟 etcd KeysAPI 接口
type MockKeysAPI struct {
	mock.Mock
}

func (m *MockKeysAPI) Get(ctx context.Context, key string, opts *client.GetOptions) (*client.Response, error) {
	args := m.Called(ctx, key, opts)
	return args.Get(0).(*client.Response), args.Error(1)
}

func (m *MockKeysAPI) Set(ctx context.Context, key, value string, opts *client.SetOptions) (*client.Response, error) {
	args := m.Called(ctx, key, value, opts)
	return args.Get(0).(*client.Response), args.Error(1)
}

func (m *MockKeysAPI) Delete(ctx context.Context, key string, opts *client.DeleteOptions) (*client.Response, error) {
	args := m.Called(ctx, key, opts)
	return args.Get(0).(*client.Response), args.Error(1)
}

func (m *MockKeysAPI) Create(ctx context.Context, key, value string) (*client.Response, error) {
	args := m.Called(ctx, key, value)
	return args.Get(0).(*client.Response), args.Error(1)
}

func (m *MockKeysAPI) CreateInOrder(ctx context.Context, dir, value string, opts *client.CreateInOrderOptions) (*client.Response, error) {
	args := m.Called(ctx, dir, value, opts)
	return args.Get(0).(*client.Response), args.Error(1)
}

func (m *MockKeysAPI) Update(ctx context.Context, key, value string) (*client.Response, error) {
	args := m.Called(ctx, key, value)
	return args.Get(0).(*client.Response), args.Error(1)
}

func (m *MockKeysAPI) Watcher(key string, opts *client.WatcherOptions) client.Watcher {
	args := m.Called(key, opts)
	return args.Get(0).(client.Watcher)
}

// 模拟 etcd Watcher 接口
type MockWatcher struct {
	mock.Mock
}

func (m *MockWatcher) Next(ctx context.Context) (*client.Response, error) {
	args := m.Called(ctx)
	val := args.Get(0)
	if val == nil {
		return nil, args.Error(1)
	}
	return val.(*client.Response), args.Error(1)
}

// 注意：EtcdServiceInterface 已经在 scheduler_service.go 中定义
