package services

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"funshion.com/upload_tuner/internal/models"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.etcd.io/etcd/client/v2"
	"go.uber.org/zap"
)

// 初始化 zap 日志记录器
func init() {
	logger, _ := zap.NewDevelopment()
	zap.ReplaceGlobals(logger)
}

// TestSchedulerEtcdIntegration 测试 SchedulerService 和 EtcdService 的交互
// 使用模拟的 etcd 客户端，避免依赖真实的 etcd 服务
func TestSchedulerEtcdIntegration(t *testing.T) {
	// 创建测试节点数据
	testNode := models.Node{
		ID:       "test-node-1",
		IP:       "*************",
		Port:     8080,
		Capacity: 100,
	}
	
	nodeJSON, err := json.Marshal(testNode)
	require.NoError(t, err)
	
	// 创建模拟的 etcd 响应
	responses := []*client.Response{
		// 添加节点响应
		{
			Action: "set",
			Node: &client.Node{
				Key:   "/nodes/test-node-1",
				Value: string(nodeJSON),
			},
		},
		// 更新节点响应
		{
			Action: "update",
			Node: &client.Node{
				Key:   "/nodes/test-node-1",
				Value: string(nodeJSON),
			},
		},
		// 删除节点响应
		{
			Action: "delete",
			Node: &client.Node{
				Key: "/nodes/test-node-1",
			},
		},
	}
	
	// 创建模拟的 Watcher
	mockWatcher := NewMockWatcher(responses)
	
	// 创建模拟的 KeysAPI
	mockKapi := new(MockKeysAPI)
	mockKapi.On("Watcher", NodePrefix, mock.Anything).Return(mockWatcher)
	
	// 创建 EtcdService 实例
	etcdService := &EtcdService{
		kapi: mockKapi,
	}
	
	// 创建模拟的 IPDBQuerier，确保返回运营商 ID 100
	mockIPDBQuerier := &MockIPDBQuerier{ReturnOperatorID: 100}

	// 创建 SchedulerService
	schedulerService := &SchedulerService{
		operatorNodes: make(map[int]map[string]models.Node),
		appSecrets:    make(map[string]models.AppSecrets),
		ipdbQuerier:   mockIPDBQuerier,
		etcdService:   etcdService,
	}
	
	// 初始化 operatorNodes 映射
	for i := 0; i < 5; i++ {
		schedulerService.operatorNodes[i] = make(map[string]models.Node)
	}
	
	// 创建通道来等待节点变更
	nodeChangeCh := make(chan struct{}, 3)
	
	// 创建一个包装函数来捕获事件
	wrapperCallback := func(node models.Node, action string) {
		// 调用 SchedulerService 的处理函数
		schedulerService.handleNodeChange(node, action)
		// 添加小的延迟，确保处理完成
		time.Sleep(100 * time.Millisecond)
		// 发送信号
		nodeChangeCh <- struct{}{}
		zap.S().Infof("测试收到节点变更事件: Action=%s, NodeID=%s", action, node.ID)
	}
	
	// 注册包装的回调函数
	etcdService.RegisterNodeCallback(wrapperCallback)
	
	// 启动 watchNodes
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	go etcdService.watchNodes(ctx)
	
	// 测试添加节点事件
	select {
	case <-nodeChangeCh:
		// 验证节点是否被正确添加到 SchedulerService
		schedulerService.mu.RLock()
		// 检查运营商 100 的映射是否存在
		nodeMap, operatorExists := schedulerService.operatorNodes[100]
		assert.True(t, operatorExists, "运营商 100 应该存在于 operatorNodes 映射中")

		// 如果运营商映射存在，检查节点是否存在
		var node models.Node
		var nodeExists bool
		if operatorExists {
			node, nodeExists = nodeMap["test-node-1"]
			assert.True(t, nodeExists, "节点应该存在于 operatorNodes[100] 映射中")
		}
		schedulerService.mu.RUnlock()

		// 如果节点存在，验证其属性
		if nodeExists {
			assert.Equal(t, testNode.ID, node.ID)
			assert.Equal(t, testNode.IP, node.IP)
			assert.Equal(t, testNode.Port, node.Port)
			assert.Equal(t, testNode.Capacity, node.Capacity)
		}
	case <-time.After(2 * time.Second):
		t.Fatal("没有收到节点添加事件")
	}
	
	// 测试更新节点事件
	select {
	case <-nodeChangeCh:
		// 验证节点是否被正确更新
		schedulerService.mu.RLock()
		// 检查运营商 100 的映射是否存在
		nodeMap, operatorExists := schedulerService.operatorNodes[100]
		assert.True(t, operatorExists, "运营商 100 应该存在于 operatorNodes 映射中")
		
		// 如果运营商映射存在，检查节点是否存在
		var node models.Node
		var nodeExists bool
		if operatorExists {
			node, nodeExists = nodeMap["test-node-1"]
			assert.True(t, nodeExists, "节点应该存在于 operatorNodes[100] 映射中")
		}
		schedulerService.mu.RUnlock()

		// 如果节点存在，验证其属性
		if nodeExists {
			assert.Equal(t, testNode.ID, node.ID)
			assert.Equal(t, testNode.IP, node.IP)
			assert.Equal(t, testNode.Port, node.Port)
			assert.Equal(t, testNode.Capacity, node.Capacity)
		}
	case <-time.After(2 * time.Second):
		t.Fatal("没有收到节点更新事件")
	}
	
	// 测试删除节点事件
	select {
	case <-nodeChangeCh:
		// 验证节点是否被正确删除
		schedulerService.mu.RLock()
		_, exists := schedulerService.operatorNodes[100]["test-node-1"]
		schedulerService.mu.RUnlock()
		
		assert.False(t, exists, "节点应该从 operatorNodes 映射中删除")
	case <-time.After(2 * time.Second):
		t.Fatal("没有收到节点删除事件")
	}
	
	// 验证 watcher 被正确调用
	mockKapi.AssertExpectations(t)
}

// 注意：使用已经定义的 MockIPDBQuerier
// 在 etcd_integration_test.go 中已经定义了这个结构体
