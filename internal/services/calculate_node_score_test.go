package services

import (
	"sync"
	"testing"
	"time"

	"funshion.com/upload_tuner/internal/models"
	"github.com/stretchr/testify/assert"
)

func TestCalculateNodeScore(t *testing.T) {
	// 创建测试用例
	tests := []struct {
		name           string
		node           models.Node
		metrics        *models.NodeMetrics
		expectedScore  float64
		metricsPresent bool
	}{
		{
			name: "无指标数据",
			node: models.Node{
				ID:       "node-1",
				IP:       "***********",
				Port:     8080,
				Capacity: 100,
			},
			metrics:        nil,
			expectedScore:  -1.0,
			metricsPresent: false,
		},
		{
			name: "理想指标数据",
			node: models.Node{
				ID:       "node-2",
				IP:       "***********",
				Port:     8080,
				Capacity: 100,
			},
			metrics: &models.NodeMetrics{
				CPUUsage:        10.0,
				MemoryUsage:     20.0,
				DiskUsage:       0.1,  // 10% 使用率，非常低
				DiskFree:        450.0, // 450GB 可用，接近最大合理值
				ConcurrentTasks: 0,    // 无并发任务
				Load1Min:        0.5,
				UpdatedAt:       time.Now(),
			},
			expectedScore:  0.94, // 0.3*(1-0.1) + 0.3*(450/500) + 0.4*(1/(1+0)) = 0.27 + 0.27 + 0.4 = 0.94
			metricsPresent: true,
		},
		{
			name: "中等指标数据",
			node: models.Node{
				ID:       "node-3",
				IP:       "***********",
				Port:     8080,
				Capacity: 100,
			},
			metrics: &models.NodeMetrics{
				CPUUsage:        50.0,
				MemoryUsage:     50.0,
				DiskUsage:       0.5,  // 50% 使用率
				DiskFree:        250.0, // 250GB 可用，一半的最大合理值
				ConcurrentTasks: 10,   // 10个并发任务
				Load1Min:        2.0,
				UpdatedAt:       time.Now(),
			},
			expectedScore:  0.336, // 0.3*(1-0.5) + 0.3*(250/500) + 0.4*(1/(1+10)) = 0.15 + 0.15 + 0.036 = 0.336
			metricsPresent: true,
		},
		{
			name: "较差指标数据",
			node: models.Node{
				ID:       "node-4",
				IP:       "***********",
				Port:     8080,
				Capacity: 100,
			},
			metrics: &models.NodeMetrics{
				CPUUsage:        90.0,
				MemoryUsage:     90.0,
				DiskUsage:       0.9,  // 90% 使用率，非常高
				DiskFree:        50.0,  // 50GB 可用，较低
				ConcurrentTasks: 100,  // 100个并发任务，非常多
				Load1Min:        8.0,
				UpdatedAt:       time.Now(),
			},
			expectedScore:  0.064, // 0.3*(1-0.9) + 0.3*(50/500) + 0.4*(1/(1+100)) = 0.03 + 0.03 + 0.004 = 0.064
			metricsPresent: true,
		},
		{
			name: "极端指标数据-磁盘使用率超过100%",
			node: models.Node{
				ID:       "node-5",
				IP:       "***********",
				Port:     8080,
				Capacity: 100,
			},
			metrics: &models.NodeMetrics{
				CPUUsage:        100.0,
				MemoryUsage:     100.0,
				DiskUsage:       1.2,   // 120% 使用率，异常值
				DiskFree:        -10.0,  // 负值，异常值
				ConcurrentTasks: 1000,  // 非常多的并发任务
				Load1Min:        20.0,
				UpdatedAt:       time.Now(),
			},
			expectedScore:  0.0004, // 0.3*0 + 0.3*0 + 0.4*(1/(1+1000)) = 0 + 0 + 0.0004 = 0.0004
			metricsPresent: true,
		},
		{
			name: "极端指标数据-磁盘空间超过最大合理值",
			node: models.Node{
				ID:       "node-6",
				IP:       "***********",
				Port:     8080,
				Capacity: 100,
			},
			metrics: &models.NodeMetrics{
				CPUUsage:        5.0,
				MemoryUsage:     10.0,
				DiskUsage:       0.05,  // 5% 使用率，非常低
				DiskFree:        1000.0, // 1000GB 可用，超过最大合理值
				ConcurrentTasks: 0,     // 无并发任务
				Load1Min:        0.1,
				UpdatedAt:       time.Now(),
			},
			expectedScore:  0.985, // 0.3*(1-0.05) + 0.3*1.0 + 0.4*(1/(1+0)) = 0.285 + 0.3 + 0.4 = 0.985
			metricsPresent: true,
		},
	}

	// 对每个测试用例执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建调度器服务
			service := &SchedulerService{
				nodeMetrics: make(map[string]models.NodeMetrics),
				metricsMu:   sync.RWMutex{},
			}

			// 如果有指标数据，添加到服务中
			if tt.metricsPresent && tt.metrics != nil {
				service.nodeMetrics[tt.node.ID] = *tt.metrics
			}

			// 调用被测试函数
			score := service.calculateNodeScore(tt.node)

			// 验证结果
			if tt.metricsPresent {
				// 对于有指标数据的情况，允许一定的浮点数误差
				assert.InDelta(t, tt.expectedScore, score, 0.01, "节点评分计算错误")
			} else {
				// 对于无指标数据的情况，期望精确的 -1.0
				assert.Equal(t, tt.expectedScore, score, "节点评分计算错误")
			}
		})
	}
}

// 测试边界情况
func TestCalculateNodeScoreEdgeCases(t *testing.T) {
	// 创建调度器服务
	service := &SchedulerService{
		nodeMetrics: make(map[string]models.NodeMetrics),
		metricsMu:   sync.RWMutex{},
	}

	// 测试空节点 ID
	t.Run("空节点ID", func(t *testing.T) {
		node := models.Node{ID: ""}
		score := service.calculateNodeScore(node)
		assert.Equal(t, -1.0, score, "空节点ID应返回-1.0")
	})

	// 由于无法直接修改常量，我们改为测试不同指标组合的情况
	// 测试磁盘使用率为0的情况
	t.Run("磁盘使用率为0", func(t *testing.T) {
		node := models.Node{ID: "test-node-disk-0"}
		service.nodeMetrics[node.ID] = models.NodeMetrics{
			DiskUsage:       0.0,  // 0% 使用率
			DiskFree:        250.0,
			ConcurrentTasks: 10,
		}

		score := service.calculateNodeScore(node)
		
		// 计算预期分数：磁盘使用率为0时，diskScore应为1.0
		expectedScore := 0.3*1.0 + 0.3*(250.0/500.0) + 0.4*(1.0/11.0)
		assert.InDelta(t, expectedScore, score, 0.01, "磁盘使用率为0处理错误")
	})

	// 测试磁盘可用空间为0的情况
	t.Run("磁盘可用空间为0", func(t *testing.T) {
		node := models.Node{ID: "test-node-free-0"}
		service.nodeMetrics[node.ID] = models.NodeMetrics{
			DiskUsage:       0.5,
			DiskFree:        0.0,  // 0GB 可用
			ConcurrentTasks: 10,
		}

		score := service.calculateNodeScore(node)
		
		// 计算预期分数：磁盘可用空间为0时，diskFreeScore应为0.0
		expectedScore := 0.3*0.5 + 0.3*0.0 + 0.4*(1.0/11.0)
		assert.InDelta(t, expectedScore, score, 0.01, "磁盘可用空间为0处理错误")
	})

	// 测试并发任务数非常大的情况
	t.Run("并发任务数非常大", func(t *testing.T) {
		node := models.Node{ID: "test-node-high-concurrent"}
		service.nodeMetrics[node.ID] = models.NodeMetrics{
			DiskUsage:       0.5,
			DiskFree:        250.0,
			ConcurrentTasks: 9999,  // 非常多的并发任务
		}

		score := service.calculateNodeScore(node)
		
		// 计算预期分数：并发任务数非常大时，concurrentScore应接近0
		expectedScore := 0.3*0.5 + 0.3*(250.0/500.0) + 0.4*(1.0/10000.0)
		assert.InDelta(t, expectedScore, score, 0.01, "并发任务数非常大处理错误")
	})
}
