package controllers

import (
	"errors"
	"fmt"
	"net"
	"net/http"
	"strconv"
	"strings"
	"time"

	"funshion.com/upload_tuner/internal/models"
	"funshion.com/upload_tuner/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"go.uber.org/zap"
)

// Define standard errors for authentication failures
var (
	ErrAuthHeaderMissing      = errors.New("authorization header is required")
	ErrAuthHeaderInvalidFmt   = errors.New("invalid Authorization header format. Use 'Bearer <token>'")
	ErrTokenInvalidOrExpired  = errors.New("invalid or expired token")
	ErrTokenClaimInvalid      = errors.New("invalid claims in token")
	ErrUserIDMismatch         = errors.New("token user ID does not match requested user ID")
	ErrUserIDMissingPathParam = errors.New("missing required path parameter: userid")
)

// SchedulerController 处理与节点调度相关的HTTP请求
type SchedulerController struct {
	schedulerService *services.SchedulerService
	authSecret       string
}

// NewSchedulerController 创建一个新的调度控制器
func NewSchedulerController(schedulerService *services.SchedulerService, secret string) *SchedulerController {
	return &SchedulerController{
		schedulerService: schedulerService,
		authSecret:       secret,
	}
}

// validateUserAuth validates the Authorization header, parses the JWT,
// verifies the user ID against the path parameter, and returns the validated user ID string or an error.
func (c *SchedulerController) validateUserAuth(userIDStr string, authHeader string) error {
	// Validate Header format "Bearer <token>"
	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
		zap.S().Warnf("Invalid Authorization header format: %s", authHeader)
		return ErrAuthHeaderInvalidFmt
	}
	tokenStr := parts[1]

	// Parse and validate JWT
	token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		// Don't forget to validate the alg is what you expect:
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// hmacSampleSecret is a []byte containing your secret, e.g. []byte("my_secret_key")
		return []byte(c.authSecret), nil
	})

	if err != nil {
		zap.S().Warnf("Failed to parse or validate JWT for UserID=%s: %v", userIDStr, err)
		// Note: err from jwt.Parse might include expiration details
		return fmt.Errorf("%w: %v", ErrTokenInvalidOrExpired, err)
	}

	// Validate claims and UserID match
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		claimUserIDFloat, ok := claims["user_id"].(float64)
		if !ok {
			zap.S().Warnf("Invalid user_id claim type in JWT for UserID=%s. Claim: %+v", userIDStr, claims["user_id"])
			return ErrTokenClaimInvalid
		}
		claimUserIDStr := strconv.FormatUint(uint64(claimUserIDFloat), 10)

		if claimUserIDStr != userIDStr {
			zap.S().Warnf("UserID mismatch for UserID=%s. Claim UserID: %s, Path UserID: %s", userIDStr, claimUserIDStr, userIDStr)
			return ErrUserIDMismatch
		}
		// Success! Return the validated user ID from the path.
		return nil
	} else {
		zap.S().Warnf("Invalid JWT claims or token invalid for UserID=%s", userIDStr)
		return ErrTokenClaimInvalid
	}
}

func (c *SchedulerController) validateAppAuth(appId string, token string, ts string) error {
	// Access the app secrets map using the service method which handles locking
	valid, err := c.schedulerService.AuthAppToken(appId, token, ts) // Use the new service method

	if !valid {
		zap.S().Warnf("App %s authentication failed: %v", appId, err)
		return fmt.Errorf("invalid app id or token") // Keep error generic for security
	}
	zap.S().Infof("App authentication successful for AppID: %s", appId)
	return nil // Validation successful
}

// GetNode 处理获取节点请求
func (c *SchedulerController) GetNode(ctx *gin.Context) {
	// 1. Get userid from path
	userIDStr := ctx.Param("userid")
	if userIDStr == "" {
		zap.S().Warn("UserID missing from request URL path")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": ErrUserIDMissingPathParam.Error()})
		return
	}

	// 2. Get Authorization Header
	authHeader := ctx.GetHeader("Authorization")
	if authHeader == "" {
		zap.S().Warn("Authorization header missing")
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": ErrAuthHeaderMissing.Error()})
		return
	}

	// 3. Validate authentication
	err := c.validateUserAuth(userIDStr, authHeader)
	if err != nil {
		// Determine the appropriate HTTP status code based on the error
		statusCode := http.StatusUnauthorized // Default to Unauthorized
		if errors.Is(err, ErrUserIDMissingPathParam) || errors.Is(err, ErrAuthHeaderInvalidFmt) {
			statusCode = http.StatusBadRequest
		}
		// Log the underlying error if it's wrapped (like token parsing errors)
		zap.S().Warnf("Authentication failed for GetNode request: %v", err)
		ctx.JSON(statusCode, gin.H{"error": err.Error()}) // Return the specific error message
		return
	}

	zap.S().Infof("Authentication successful for UserID=%s, proceeding to get node.", userIDStr)
	// 4. 获取用户IP地址
	// 检查是否有 src 参数指定 IP 地址
	clientIP := ctx.ClientIP()
	if srcIP := ctx.Query("src"); srcIP != "" {
		// 验证是否为有效的点分十进制 IP 地址
		if net.ParseIP(srcIP) != nil {
			clientIP = srcIP
			zap.S().Infow("Using src parameter as client IP", "srcIP", srcIP)
		} else {
			zap.S().Warnw("Invalid src IP parameter, using real client IP instead", "srcIP", srcIP, "clientIP", clientIP)
		}
	}
	zap.S().Infow("Request received for GetNode", "token", authHeader, "clientIP", clientIP)

	// 4. 获取可用节点 (Token 验证通过后执行)
	node, err := c.schedulerService.SelectNode(clientIP)
	if err != nil {
		// Check if the specific error is "no available nodes"
		if errors.Is(err, services.ErrNoNodes) { // Use errors.Is for proper error comparison
			zap.S().Warnf("No available nodes found for scheduling request for UserID=%s", userIDStr)
			ctx.JSON(http.StatusServiceUnavailable, gin.H{"error": "No available nodes"})
		} else {
			zap.S().Errorf("Error selecting node for UserID=%s: %v", userIDStr, err)
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to select node"})
		}
		return
	}

	// 3. Return node address
	zap.S().Infof("Selected node %s (%s:%d) for request from UserID=%s", node.ID, node.IP, node.Port, userIDStr)
	ctx.JSON(http.StatusOK, gin.H{"node_address": fmt.Sprintf("%s:%d", node.IP, node.Port)})
}

// GetNodeForApp 获取应用节点
func (c *SchedulerController) GetNodeForApp(ctx *gin.Context) {
	// 1. Validate authentication and get AppID
	appId := ctx.Param("appid")
	if appId == "" {
		zap.S().Warn("AppID missing from request URL path")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "AppID missing"})
		return
	}
	// 2. get token from url parameter
	token := ctx.Query("token")
	if token == "" {
		zap.S().Warn("Token missing from request URL query")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Token missing"})
		return
	}

	tsParaStr := ctx.Query("ts")
	if tsParaStr == "" {
		zap.S().Warn("Timestamp 'ts' missing from request URL query")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Timestamp 'ts' missing"})
		return
	}
	cliIp := ctx.Query("cip")
	if cliIp == "" {
		zap.S().Warn("Client ip 'cip' missing from request URL query")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Client ip 'cip' is missing"})
		return
	}

	// Parse the timestamp string into an integer (Unix seconds)
	tsSeconds, err := strconv.ParseInt(tsParaStr, 10, 64)
	if err != nil {
		zap.S().Warnf("Invalid timestamp format for 'ts': %s, error: %v", tsParaStr, err)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid timestamp format"})
		return
	}

	// Convert Unix seconds to time.Time
	requestTime := time.Unix(tsSeconds, 0)
	currentTime := time.Now()

	// 检查当前时间是否超过了截止时间时间戳
	// Use requestTime here instead of tsPara
	if currentTime.After(requestTime) {
		zap.S().Warnf("Request timestamp expired for AppID %s. Request time: %s, Current time: %s", appId, requestTime, currentTime)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Request link has expired"}) // "访问链接已过期"
		return
	}

	// 3. Validate authentication (using original token and ts string)
	err = c.validateAppAuth(appId, token, tsParaStr) // Pass the original ts string
	if err != nil {
		zap.S().Warnf("App authentication failed for AppID '%s': %v", appId, err)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()}) // Use 401 Unauthorized
		return
	}

	zap.S().Infof("App authentication successful for AppID: %s", appId)

	// 4. Authentication successful, select a node
	node, err := c.schedulerService.SelectNode(cliIp)
	if err != nil {
		if errors.Is(err, services.ErrNoNodes) {
			zap.S().Warnf("No nodes available for AppID: %s", appId)
			ctx.JSON(http.StatusServiceUnavailable, gin.H{"error": "No nodes available"})
		} else {
			zap.S().Errorf("Failed to select node for AppID %s: %v", appId, err)
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to select node"})
		}
		return
	}

	zap.S().Infof("Assigned node %s (%s:%d) to AppID: %s", node.ID, node.IP, node.Port, appId)
	ctx.JSON(http.StatusOK, gin.H{"node_address": fmt.Sprintf("%s:%d", node.IP, node.Port)})
}

// GetAllNodes 获取所有注册的节点信息 (管理接口)
func (c *SchedulerController) GetAllNodes(ctx *gin.Context) {
	// 这里可以添加管理员权限验证
	nodes := c.schedulerService.GetAllNodes()
	zap.S().Debugf("Retrieved %d nodes for GetNodes request", len(nodes))
	ctx.JSON(http.StatusOK, nodes)
}

// UpdateNodeMetrics 处理节点指标上报请求
func (c *SchedulerController) UpdateNodeMetrics(ctx *gin.Context) {
	nodeID := ctx.Param("nodeID") // 获取URL中的nodeID
	if nodeID == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "缺少 nodeID"})
		return
	}

	var metrics models.NodeMetrics
	if err := ctx.ShouldBindJSON(&metrics); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "无效的指标数据: " + err.Error()})
		return
	}

	// 调用服务层更新指标
	err := c.schedulerService.UpdateNodeMetrics(nodeID, metrics)
	if err != nil {
		zap.S().Errorf("Failed to update metrics for node %s: %v", nodeID, err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to record metrics"})
		return
	}
	zap.S().Debugf("Successfully recorded metrics for node %s", nodeID)
	ctx.JSON(http.StatusOK, gin.H{"status": "Metrics recorded successfully"})
}
