package models

import "time"

// Node 表示一个可用节点
type Node struct {
	ID           string `json:"id"`       // 节点唯一标识
	IP           string `json:"ip"`       // 节点 IP 地址
	Port         int    `json:"port"`     // 节点端口号
	Capacity int    `json:"capacity"` // 最大承载能力（如最大并发数）
}

// NodeMetrics 表示节点的动态变化指标
// 可根据实际需求增减字段
type NodeMetrics struct {
	CPUUsage    float64 `json:"cpu_usage"`    // CPU 使用率（百分比）
	MemoryUsage float64 `json:"memory_usage"` // 内存使用率（百分比）
	DiskUsage   float64 `json:"disk_usage"`   // 磁盘使用率（百分比）
	DiskFree    float64 `json:"disk_free"`    // 磁盘剩余可用空间 (GB)
	ConcurrentTasks int       `json:"concurrent_tasks"` // 并发任务数
	Load1Min        float64   `json:"load_1min"`        // 1分钟平均负载
	UpdatedAt       time.Time `json:"updated_at"`       // 指标更新时间
}

// NodeResponse 表示节点响应
type NodeResponse struct {
	Address string `json:"address"` // 分配的节点地址
}

type AppSecrets struct {
	AppID  string `json:"app_id"`
	Secret string `json:"secret"`
}
