package configs

// ServerConfig holds configuration for an HTTP server.
type ServerConfig struct {
	Address string `mapstructure:"address"` // e.g., "0.0.0.0"
	Port    int    `mapstructure:"port"`    // e.g., 8080
}

// EtcdConfig holds configuration for connecting to Etcd.
type EtcdConfig struct {
	Endpoints []string `mapstructure:"endpoints"` // List of Etcd server endpoints
}

// LogFileConfig holds configuration specific to file logging.
type LogFileConfig struct {
	Enable     bool   `mapstructure:"enable"`     // Enable file logging
	Path       string `mapstructure:"path"`       // Log file path (e.g., "logs/app.log")
	MaxSize    int    `mapstructure:"maxsize"`    // Max size in megabytes before rotation
	MaxBackups int    `mapstructure:"maxbackups"` // Max number of old log files to keep
	MaxAge     int    `mapstructure:"maxage"`     // Max number of days to keep old log files
	Compress   bool   `mapstructure:"compress"`   // Compress rotated log files
}

// LogConfig holds the overall logging configuration.
type LogConfig struct {
	Level        string        `mapstructure:"level"`        // Log level (debug, info, warn, error, dpanic, panic, fatal)
	Console      bool          `mapstructure:"console"`      // Enable console logging
	File         LogFileConfig `mapstructure:"file"`         // File logging configuration
	ReportCaller bool          `mapstructure:"report_caller"` // Whether to report the caller function
}

// AppConfig holds the overall application configuration.
type AppConfig struct {
	Mode    string       `mapstructure:"mode"`    // 运行模式：debug 或 release
	API     ServerConfig `mapstructure:"api"`     // Configuration for the main API server
	Metrics ServerConfig `mapstructure:"metrics"` // Configuration for the metrics server
	Etcd    EtcdConfig   `mapstructure:"etcd"`    // Configuration for Etcd connection
	Log     LogConfig    `mapstructure:"log"`     // Logging configuration
	AuthSecret string       `mapstructure:"auth_secret"` // Static secret key for token validation
}
