# Default configuration for Upload Tuner

# 运行模式配置 (debug 或 release)
mode: "debug"      # 可选值: debug, release

# API Server configuration
api:
  address: "0.0.0.0" # Listen on all available network interfaces
  port: 8081        # Default port for the main API

# Metrics Server configuration (Prometheus metrics endpoint)
metrics:
  address: "0.0.0.0"
  port: 8082        # Default port for metrics

# Authentication configuration
auth_secret: "TU/wO3eJdqbTppQ21qU6QVvrBFMb7I6qTfsDwB6FINc="  # 用于令牌验证的密钥

# Etcd configuration
etcd:
  # List of etcd cluster endpoints
  endpoints:
    - "http://*************:2379" # Default local etcd endpoint
    # Add more endpoints if you have an etcd cluster
    # - "etcd-node2:2379"
    # - "etcd-node3:2379"

# Logging configuration
log:
  level: "info"        # Default log level (options: debug, info, warn, error, dpanic, panic, fatal)


  # File logging configuration
  file:
    enable: true              # Enable logging to file
    path: "logs/app.log"    # Path to the log file (relative to executable or absolute)
    maxsize: 100              # Max size in MB before rotation
    maxbackups: 5              # Max number of old log files to keep
    maxage: 30               # Max number of days to keep old log files
    compress: false           # Compress rotated files (e.g., into .gz)
