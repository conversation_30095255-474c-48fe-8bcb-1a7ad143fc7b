# Upload Tuner

Upload Tuner 是一个基于 Go 语言和 Gin 框架开发的节点调度服务，用于在多个节点间进行负载均衡。

## 项目结构

```
upload_tuner/
├── configs/           # 配置文件
├── docs/              # 文档
├── internal/          # 内部代码
│   ├── controllers/   # 控制器
│   ├── models/        # 数据模型
│   └── services/      # 业务逻辑服务
├── ipdb/              # IP 数据库
├── logs/              # 日志文件
├── routes/            # 路由配置
├── scripts/           # 脚本文件
├── tools/             # 工具脚本
├── go.mod             # Go 模块定义
├── go.sum             # Go 依赖校验
└── main.go            # 主程序入口
```

## 功能特性

- 节点调度：根据负载均衡算法从可用节点列表中选择一个节点
- 用户认证：支持 JWT 令牌验证
- 应用认证：支持基于 MD5 的应用密钥验证
- 节点监控：实时监控节点状态和指标
- 分布式存储：使用 etcd 存储节点信息和应用密钥

## 快速开始

### 构建

```bash
make
```

### 运行

```bash
./upload_tuner
```

## API 接口

### 用户节点分配接口

```
GET /user/node/:userid
```

请求头：
```
Authorization: Bearer <JWT令牌>
```

响应：
```json
{
  "node_address": "*************:8080"
}
```

### 应用节点分配接口

```
GET /app/node/:appid?token=<令牌>&ts=<时间戳>&cip=<客户端IP>
```

响应：
```json
{
  "node_address": "*************:8080"
}
```

### 管理接口

```
GET /admin/nodes
```

响应：
```json
{
  "nodes": [
    {
      "id": "node-123",
      "ip": "*************",
      "port": 8080,
      "capacity": 100
    }
  ]
}
```

## 工具脚本

### 测试用户节点 API

`tools/test_user_node_api.py` 用于测试用户节点分配接口。

#### 使用方法

```bash
./tools/test_user_node_api.py [--host <服务器地址>] [--userid <用户ID>] [--secret <JWT密钥>] [-v]
```

参数说明：
- `--host`：API 服务器地址，默认为 http://localhost:8081
- `--userid`：测试用户 ID，默认为 123
- `--secret`：JWT 密钥，默认为配置文件中的密钥
- `-v, --verbose`：显示详细输出

#### 测试内容

1. 正常请求测试：使用有效的用户 ID 和 JWT 令牌
2. 缺少认证头测试：验证缺少 Authorization 头时的错误处理
3. 无效用户 ID 测试：验证无效用户 ID 时的错误处理
4. 无效令牌测试：验证无效 JWT 令牌时的错误处理
5. 用户 ID 不匹配测试：验证 JWT 令牌中的用户 ID 与请求路径中的不匹配时的错误处理

### 测试应用节点 API

`tools/test_app_node_api.py` 用于测试应用节点分配接口。

#### 使用方法

```bash
./tools/test_app_node_api.py [--host <服务器地址>] [--appid <应用ID>] [--secret <应用密钥>] [--cip <客户端IP>] [-v]
```

参数说明：
- `--host`：API 服务器地址，默认为 http://localhost:8081
- `--appid`：测试应用 ID，默认为 test-app-123
- `--secret`：应用密钥，默认为 app-secret-key
- `--cip`：客户端 IP 地址，默认为 *************
- `-v, --verbose`：显示详细输出

#### 测试内容

1. 正常请求测试：使用有效的应用 ID、令牌、时间戳和客户端 IP
2. 缺少令牌测试：验证缺少 token 参数时的错误处理
3. 缺少时间戳测试：验证缺少 ts 参数时的错误处理
4. 缺少客户端 IP 测试：验证缺少 cip 参数时的错误处理
5. 无效令牌测试：验证无效令牌时的错误处理
6. 过期时间戳测试：验证过期时间戳时的错误处理

### 添加应用密钥

`tools/add_app_secret.py` 用于向 etcd 中添加应用密钥。

#### 使用方法

1. 添加应用密钥：
```bash
./tools/add_app_secret.py --app-id <应用ID> --secret <密钥> [--etcd-endpoint <etcd端点>]
```

2. 添加应用密钥并测试：
```bash
./tools/add_app_secret.py --app-id <应用ID> --secret <密钥> --test
```

3. 列出所有应用密钥：
```bash
./tools/add_app_secret.py --app-id list
```

4. 显示详细输出：
```bash
./tools/add_app_secret.py --app-id <应用ID> --secret <密钥> --test -v
```

参数说明：
- `--app-id`：应用 ID，使用 "list" 可列出所有应用密钥
- `--secret`：应用密钥
- `--etcd-endpoint`：etcd 端点地址，默认为 http://*************:2379
- `--test`：测试密钥是否有效
- `-v, --verbose`：显示详细输出

#### 功能说明

1. 写入应用密钥：向 etcd 中写入指定的应用 ID 和密钥
2. 读取应用密钥：从 etcd 中读取指定应用 ID 的密钥信息
3. 测试应用密钥：生成测试用的令牌和 API 调用示例
4. 列出所有应用密钥：列出 etcd 中所有已配置的应用密钥

### 启动 etcd 集群

`scripts/start_etcd_cluster.sh` 用于启动一个本地 etcd 集群。

#### 使用方法

```bash
./scripts/start_etcd_cluster.sh
```

#### 功能说明

1. 启动多个 etcd 节点组成集群
2. 启动 gRPC 代理，提供统一的访问入口
3. 检查端口冲突并在启动前退出
4. 等待节点健康后再启动代理
5. 提供优雅的关闭功能

## 配置说明

配置文件位于 `configs/config.yaml`，主要包含以下配置项：

- API 服务器配置：地址和端口
- 指标服务器配置：地址和端口
- 认证配置：JWT 密钥
- etcd 配置：集群端点
- 日志配置：日志级别和文件路径

## 开发指南

### 依赖管理

项目使用 Go Modules 进行依赖管理：

```bash
# 添加依赖
go get <package>

# 更新依赖
go get -u <package>

# 整理依赖
go mod tidy
```

### 测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/services

# 运行特定测试函数
go test -v ./internal/services -run TestCalculateNodeScore
```

### 代码风格

项目遵循 Go 标准代码风格，使用以下工具进行格式化和检查：

```bash
# 格式化代码
go fmt ./...

# 检查代码风格
golint ./...

# 检查代码问题
go vet ./...
```

## 许可证

[MIT License](LICENSE)
