# Bin name
BINARY_NAME=upload_tuner

# Get build information
GIT_COMMIT=$(shell git rev-parse --short HEAD)
GIT_TAG=$(shell git describe --tags --always)
BUILD_TIME=$(shell date -u +'%Y-%m-%dT%H:%M:%SZ')

# Go build flags
# Adjust the path 'main.GitCommit' etc., if your variables are defined in a different package
LDFLAGS=-ldflags "\
    -X 'main.GitCommit=${GIT_COMMIT}' \
    -X 'main.GitTag=${GIT_TAG}' \
    -X 'main.BuildTime=${BUILD_TIME}'"

# Default target
all: build

# Build the application
build:
	@echo "Building ${BINARY_NAME}..."
	@go build ${LDFLAGS} -o ${BINARY_NAME} main.go
	@echo "Build complete: ${BINARY_NAME}"

# Run the application
run:
	@go run ${LDFLAGS} main.go

# Clean build artifacts
clean:
	@echo "Cleaning..."
	@go clean
	@rm -f ${BINARY_NAME}

# Run tests
test:
	@echo "Running unit tests..."
	@go test -v ./...

# Run integration tests
integration-test:
	@echo "Running etcd integration tests..."
	@ETCD_ENDPOINT="http://192.168.16.61:8080" ETCD_API_KEY="secret_key_123" go test -v ./internal/services -run TestEtcdIntegration

# Show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make build            Build the application'
	@echo ' make run              Run the application (includes build info)'
	@echo ' make clean            Clean build artifacts'
	@echo ' make test             Run all unit tests'
	@echo ' make integration-test Run etcd integration tests'
	@echo ''

.PHONY: all build run clean test integration-test help
