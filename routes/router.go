package routes

import (
	"net/http"

	"funshion.com/upload_tuner/internal/controllers"
	"github.com/gin-gonic/gin"
)

// CORSMiddleware 处理跨域请求中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent) // 返回204状态码
			return
		}

		c.Next()
	}
}

// SetupAPIRouter 配置 API 相关路由
func SetupAPIRouter(schedulerController *controllers.SchedulerController, gitCommit, gitTag, buildTime, startTime string) *gin.Engine {
	// 创建默认的gin引擎
	r := gin.Default()

	// 使用CORS中间件
	r.Use(CORSMiddleware())

	// 定义基本路由
	r.GET("/ping", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "pong api",
		})
	})

	// 节点调度相关API
	// 用户获取节点地址的接口, userid 在路径中, token 在请求头的 Authorization 字段中
	r.GET("/user/node/:userid", schedulerController.GetNode)

	r.GET("/app/node/:appid", schedulerController.GetNodeForApp)

	// 添加获取所有节点的管理接口 (如果需要暴露给API端口)
	r.GET("/admin/nodes", schedulerController.GetAllNodes)

	// Version endpoint
	r.GET("/version", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"commit":     gitCommit,
			"tag":        gitTag,
			"build_time": buildTime,
			"start_time": startTime,
		})
	})

	// Add health check or other non-API routes here if needed
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "UP"})
	})

	return r
}

// SetupMetricsRouter 配置指标上报相关路由
func SetupMetricsRouter(schedulerController *controllers.SchedulerController) *gin.Engine {
	// 创建默认的gin引擎
	r := gin.Default()

	// 定义基本路由
	r.GET("/ping", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "pong metrics",
		})
	})

	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "UP"})
	})

	// 节点指标上报接口
	r.POST("/metrics/:nodeID", schedulerController.UpdateNodeMetrics)

	return r
}
