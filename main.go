package main

import (
	"flag"
	"fmt"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"funshion.com/upload_tuner/configs"
	"funshion.com/upload_tuner/internal/controllers"
	"funshion.com/upload_tuner/internal/ginmode"
	"funshion.com/upload_tuner/internal/logger"
	"funshion.com/upload_tuner/internal/services"
	"funshion.com/upload_tuner/ipdb"
	"funshion.com/upload_tuner/routes"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// 这些变量将在构建时通过 ldflags 设置
var (
	GitCommit string
	GitTag    string
	BuildTime string

	// Command-line flag
	showVersion = flag.Bool("version", false, "Print version information and exit")

	// Variable to store application start time
	startTime string

	//
	cfg configs.AppConfig
	
)

const (
	ipv4DataPath = "configs/merged_ip_range.txt"
	ipv6DataPath = "configs/merged_ipv6_dat.txt"
)

func init() {
	// Record start time when the package is initialized
	startTime = time.Now().UTC().Format(time.RFC3339)
}

// loadConfig loads configuration from file and environment variables.
func loadConfig() (configs.AppConfig, error) {
	var config configs.AppConfig

	v := viper.New()
	v.SetConfigName("config")
	v.SetConfigType("yaml")
	v.AddConfigPath("./configs")
	v.AddConfigPath(".")
	v.AddConfigPath("/etc/upload_tuner/")

	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			fmt.Println("Config file not found, using defaults/environment variables.")
		} else {
			return config, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	if err := v.Unmarshal(&config); err != nil {
		return config, fmt.Errorf("unable to decode config into struct: %w", err)
	}

	if config.API.Port == 0 {
		config.API.Port = 8080
	}
	if config.API.Address == "" {
		config.API.Address = "0.0.0.0"
	}
	if config.Metrics.Port == 0 {
		config.Metrics.Port = 9090
	}
	if config.Metrics.Address == "" {
		config.Metrics.Address = "0.0.0.0"
	}
	if len(config.Etcd.Endpoints) == 0 {
		config.Etcd.Endpoints = []string{"127.0.0.1:2379"}
	}

	// Validate essential configurations
	if config.AuthSecret == "" {
		return config, fmt.Errorf("configuration error: auth_secret cannot be empty")
	}

	fmt.Printf("Configuration loaded (pre-logger init): %+v\n", config)

	return config, nil
}

func main() {
	// 先解析命令行参数，以便能够处理版本标志
	flag.Parse()

	// 检查是否显示版本信息
	if *showVersion {
		fmt.Printf("Build Information:\n")
		fmt.Printf("  Commit: %s\n", GitCommit)
		fmt.Printf("  Tag:    %s\n", GitTag)
		fmt.Printf("  Build Time: %s\n", BuildTime)
		os.Exit(0)
	}

	var err error
	cfg, err = loadConfig()
	if err != nil {
		fmt.Printf("FATAL: Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

	if err := logger.InitLogger(cfg.Log); err != nil {
		fmt.Printf("FATAL: Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}
	defer func() { _ = zap.L().Sync() }()

	// Initialize IPDB
	if err := ipdb.Init(ipv4DataPath, ipv6DataPath); err != nil {
		// 同时向控制台和日志输出错误信息
		fmt.Printf("FATAL: Failed to initialize IPDB: %v\n", err)
		zap.L().Fatal("Failed to initialize IPDB", zap.Error(err))
		// 确保程序退出
		os.Exit(1)
	} else {
		zap.L().Info("IPDB initialized successfully")
	}



	zap.L().Info("Starting application...",
		zap.String("commit", GitCommit),
		zap.String("tag", GitTag),
		zap.String("build_time", BuildTime),
		zap.String("start_time", startTime),
	)

	// 根据配置设置Gin的运行模式
	ginmode.SetMode(cfg.Mode)
	zap.L().Info("Gin mode set", zap.String("mode", cfg.Mode))

	nodeService := services.NewSchedulerService(cfg.Etcd)
	// Pass the loaded AuthSecret to the controller
	schedulerController := controllers.NewSchedulerController(nodeService, cfg.AuthSecret)

	apiRouter := routes.SetupAPIRouter(schedulerController, GitCommit, GitTag, BuildTime, startTime)
	metricsRouter := routes.SetupMetricsRouter(schedulerController)

	var wg sync.WaitGroup
	apiAddr := fmt.Sprintf("%s:%d", cfg.API.Address, cfg.API.Port)
	metricsAddr := fmt.Sprintf("%s:%d", cfg.Metrics.Address, cfg.Metrics.Port)

	wg.Add(1)
	go func() {
		defer wg.Done()
		zap.L().Info("Starting API server", zap.String("address", apiAddr))
		if err := http.ListenAndServe(apiAddr, apiRouter); err != nil && err != http.ErrServerClosed {
			zap.L().Fatal("Failed to start API server", zap.Error(err))
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		zap.L().Info("Starting Metrics server", zap.String("address", metricsAddr))
		if err := http.ListenAndServe(metricsAddr, metricsRouter); err != nil && err != http.ErrServerClosed {
			zap.L().Fatal("Failed to start Metrics server", zap.Error(err))
		}
	}()

	wg.Wait()
	zap.L().Info("Application servers stopped.")
}
