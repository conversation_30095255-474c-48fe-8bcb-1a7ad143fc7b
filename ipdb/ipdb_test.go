package ipdb

import (
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
)

// Helper to reset global state for testing Init multiple times or with different outcomes.
// WARNING: Directly reassigning sync.Once is generally not a good practice for production code
// but can be a pragmatic approach in tests for package-level singletons.
// A better long-term solution would be to refactor ipdb to be more testable,
// perhaps by making the IP ranges part of a struct rather than package-level variables.
func resetIPDBState() {
	mu.Lock()
	ipv4Ranges = nil
	ipv6Ranges = nil
	initErr = nil
	once = sync.Once{} // Re-initialize sync.Once
	mu.Unlock()
}

// Helper to create a temporary file with given content for testing.
// It returns the path to the temporary file and a cleanup function.
func createTempFile(t *testing.T, content string) (string, func()) {
	t.Helper()
	tmpFile, err := ioutil.TempFile("", "test_ip_data_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}

	if _, err := tmpFile.WriteString(content); err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		t.Fatalf("Failed to write to temp file: %v", err)
	}

	filePath := tmpFile.Name()
	if err := tmpFile.Close(); err != nil {
		os.Remove(filePath)
		t.Fatalf("Failed to close temp file: %v", err)
	}

	cleanup := func() {
		os.Remove(filePath)
	}
	return filePath, cleanup
}

func TestIPDB_SuccessfulInitAndQuery(t *testing.T) {
	resetIPDBState()
	ipv4Content := `
******* ********* 电信
******* ********** 联通
*********** ***********00 移动
`
	ipv6Content := `
2001:db8:1:: 2001:db8:1::ffff 移动
2001:db8:2:: 2001:db8:2::ffff 电信
`
	ipv4File, cleanupv4 := createTempFile(t, ipv4Content)
	defer cleanupv4()
	ipv6File, cleanupv6 := createTempFile(t, ipv6Content)
	defer cleanupv6()

	err := Init(ipv4File, ipv6File)
	if err != nil {
		t.Fatalf("Init() failed: %v", err)
	}

	testCases := []struct {
		name         string
		ip           string
		expectedOp   int
		expectErr    bool
		preInitCheck bool // if true, test query before successful init
	}{
		// IPv4 tests
		{"IPv4 Telecom In Range", "*********", Telecom, false, false},
		{"IPv4 Telecom Start Range", "*******", Telecom, false, false},
		{"IPv4 Telecom End Range", "*********", Telecom, false, false},
		{"IPv4 Unicom In Range", "*********", Unicom, false, false},
		{"IPv4 Mobile In Range", "************", Mobile, false, false},
		{"IPv4 Mobile Start Range", "***********", Mobile, false, false},
		{"IPv4 Mobile End Range", "***********00", Mobile, false, false},
		{"IPv4 Not Found", "********", UnknownOperator, false, false},
		{"IPv4 Between Ranges", "*******", UnknownOperator, false, false},

		// IPv6 tests
		{"IPv6 Mobile In Range", "2001:db8:1::10", Mobile, false, false},
		{"IPv6 Mobile Start Range", "2001:db8:1::", Mobile, false, false},
		{"IPv6 Mobile End Range", "2001:db8:1::ffff", Mobile, false, false},
		{"IPv6 Telecom In Range", "2001:db8:2::aa", Telecom, false, false},
		{"IPv6 Not Found", "2001:db9::1", UnknownOperator, false, false},
		{"IPv6 Between Ranges", "2001:db8:1:ffff::1", UnknownOperator, false, false}, // After first IPv6 range

		// Error cases
		{"Invalid IP Format", "not-an-ip", UnknownOperator, true, false},
		{"Empty IP String", "", UnknownOperator, true, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			op, err := Query(tc.ip)
			if tc.expectErr {
				if err == nil {
					t.Errorf("Query(%s): expected error, got nil", tc.ip)
				}
			} else {
				if err != nil {
					t.Errorf("Query(%s): unexpected error: %v", tc.ip, err)
				}
				if op != tc.expectedOp {
					t.Errorf("Query(%s): expected operator %d, got %d", tc.ip, tc.expectedOp, op)
				}
			}
		})
	}
}

func TestIPDB_Query_BeforeInit(t *testing.T) {
	resetIPDBState() // Ensure ipdb state is fresh, initErr is nil initially by reset

	// Manually set initErr to simulate a state where Init was never called or failed
	// This is a bit artificial because sync.Once means Init's internal logic only runs once.
	// If Init was never called, initErr would be nil, and query would try to operate on nil slices.
	// To properly test "query before successful Init", Init must be called and fail.
	// Let's first test with a truly uninitialized state (which our reset achieves)
	// This should ideally lead to some form of safe handling in Query or loadRangesFromFile if ranges are nil.
	// The current Query checks initErr. If initErr is nil (as after resetIPDBState()),
	// Query will proceed and likely operate on nil slices if Init was never run to populate them.

	_, err := Query("*******")
	// If initErr is checked and is nil (because Init hasn't run and failed),
	// and Query proceeds, it might return UnknownOperator for nil slices.
	// A more robust Query might check if slices are nil too, or rely solely on initErr.
	// Given current code: Query relies on initErr. If Init() wasn't called, initErr is nil.
	// The query will run on empty slices.
	if err != nil {
		t.Errorf("Query() before Init() with no initErr: expected nil error for empty slices, got %v", err)
	}

	// Now test if Init() actually failed
	resetIPDBState()
	nonExistentFile := filepath.Join(t.TempDir(), "non_existent_v4.txt")
	_ = Init(nonExistentFile, nonExistentFile) // This will set initErr

	_, err = Query("*******")
	if err == nil {
		t.Errorf("Query() after failed Init(): expected error, got nil")
	}
	if initErr == nil { // Check the global initErr
		t.Errorf("Expected initErr to be non-nil after failed Init()")
	}
}

func TestIPDB_Init_Failures(t *testing.T) {
	testCases := []struct {
		name        string
		ipv4Content string
		ipv6Content string
		expectedErr string // Substring of the expected error
	}{
		{
			name:        "IPv4 File Not Exist",
			ipv4Content: "FILE_DOES_NOT_EXIST", // Special marker
			ipv6Content: "",
			expectedErr: "opening file",
		},
		{
			name:        "IPv6 File Not Exist",
			ipv4Content: "",
			ipv6Content: "FILE_DOES_NOT_EXIST", // Special marker
			expectedErr: "opening file",
		},
		{
			name:        "Malformed Line IPv4 - Too Few Fields",
			ipv4Content: "******* *******", // Missing operator
			ipv6Content: "",
			expectedErr: "invalid format, expected 3 fields",
		},
		{
			name:        "Malformed Line IPv6 - Too Many Fields",
			ipv4Content: "",
			ipv6Content: "2001::1 2001::2 移动 extra",
			expectedErr: "invalid format, expected 3 fields",
		},
		{
			name:        "Invalid Start IP IPv4",
			ipv4Content: "bad-ip ******* 电信",
			ipv6Content: "",
			expectedErr: "invalid start IP",
		},
		{
			name:        "Invalid End IP IPv6",
			ipv4Content: "",
			ipv6Content: "2001::1 bad-ip 移动",
			expectedErr: "invalid end IP",
		},
		{
			name:        "Unknown Operator IPv4",
			ipv4Content: "******* ******* 未知公司",
			ipv6Content: "",
			expectedErr: "unknown operator name",
		},
		{
			name:        "Start IP > End IP IPv4",
			ipv4Content: "******** ******* 电信",
			ipv6Content: "",
			expectedErr: "start IP", // "is greater than end IP"
		},
		{
			name:        "Invalid IPv4 in IPv4 file (e.g. IPv6 presented)",
			ipv4Content: "2001::1 2001::2 电信",
			ipv6Content: "",
			expectedErr: "not a valid IPv4 address",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			resetIPDBState()

			var v4Path, v6Path string
			var cleanupv4, cleanupv6 func()

			if tc.ipv4Content == "FILE_DOES_NOT_EXIST" {
				v4Path = filepath.Join(t.TempDir(), "non_existent_v4.txt")
			} else {
				v4Path, cleanupv4 = createTempFile(t, tc.ipv4Content)
				defer cleanupv4()
			}

			if tc.ipv6Content == "FILE_DOES_NOT_EXIST" {
				v6Path = filepath.Join(t.TempDir(), "non_existent_v6.txt")
			} else {
				v6Path, cleanupv6 = createTempFile(t, tc.ipv6Content)
				defer cleanupv6()
			}

			err := Init(v4Path, v6Path)
			if err == nil {
				t.Fatalf("Init() with problematic content: expected error, got nil")
			}
			// fmt.Printf("Got error: %v\n", err) // For debugging
			if !contains(err.Error(), tc.expectedErr) {
				t.Errorf("Init() error mismatch: expected to contain '%s', got '%v'", tc.expectedErr, err)
			}

			// Verify initErr is set
			mu.RLock()
			globalInitErr := initErr
			mu.RUnlock()
			if globalInitErr == nil {
				t.Error("Expected global initErr to be set after a failed Init()")
			}
		})
	}
}

func TestIPDB_Init_EmptyFiles(t *testing.T) {
	resetIPDBState()
	ipv4File, cleanupv4 := createTempFile(t, "")
	defer cleanupv4()
	ipv6File, cleanupv6 := createTempFile(t, "")
	defer cleanupv6()

	err := Init(ipv4File, ipv6File)
	if err != nil {
		t.Fatalf("Init() with empty files failed: %v", err)
	}

	op, queryErr := Query("*******")
	if queryErr != nil {
		t.Errorf("Query() after Init with empty files: unexpected error: %v", queryErr)
	}
	if op != UnknownOperator {
		t.Errorf("Query() after Init with empty files: expected UnknownOperator, got %d", op)
	}
}

func TestIPDB_Init_MultipleCalls(t *testing.T) {
	resetIPDBState()
	ipv4Content := "******* ********* 电信"
	ipv4File, cleanupv4 := createTempFile(t, ipv4Content)
	defer cleanupv4()
	emptyFile, cleanupEmpty := createTempFile(t, "")
	defer cleanupEmpty()

	// First call, should succeed and load data
	err1 := Init(ipv4File, emptyFile)
	if err1 != nil {
		t.Fatalf("First Init() call failed: %v", err1)
	}
	op1, _ := Query("*******")
	if op1 != Telecom {
		t.Errorf("After first Init, expected Telecom, got %d", op1)
	}

	// Second call, with different (potentially failing or empty) files.
	// Due to sync.Once, the loadRangesFromFile logic should not run again.
	// initErr should remain nil from the first successful call.
	// ipv4Ranges and ipv6Ranges should still hold data from the first call.
	failingPath := filepath.Join(t.TempDir(), "non_existent_v4_for_second_call.txt")
	err2 := Init(failingPath, failingPath) // These files don't exist
	if err2 != nil {
		// This checks the return of Init, which returns the stored initErr by sync.Once
		t.Fatalf("Second Init() call returned an unexpected error: %v. Expected nil as first call succeeded.", err2)
	}

	mu.RLock()
	currentInitErr := initErr // Check the actual global initErr
	mu.RUnlock()
	if currentInitErr != nil {
		t.Fatalf("Global initErr became non-nil after second Init call: %v", currentInitErr)
	}


	op2, _ := Query("*******")
	if op2 != Telecom {
		t.Errorf("After second Init call, expected Telecom (from first load), got %d", op2)
	}

	// Test that a third call also doesn't change things
	err3 := Init(emptyFile, emptyFile)
	if err3 != nil {
		t.Fatalf("Third Init() call returned an unexpected error: %v", err3)
	}
	op3, _ := Query("*******")
	if op3 != Telecom {
		t.Errorf("After third Init call, expected Telecom, got %d", op3)
	}
}


func TestIPDB_ConcurrentQueries(t *testing.T) {
	resetIPDBState()
	ipv4Content := "******* ********* 移动\n2.2.2.0 ********* 电信"
	ipv4File, cleanupv4 := createTempFile(t, ipv4Content)
	defer cleanupv4()
	emptyFile, cleanupEmpty := createTempFile(t, "")
	defer cleanupEmpty()

	err := Init(ipv4File, emptyFile)
	if err != nil {
		t.Fatalf("Init() failed: %v", err)
	}

	numGoroutines := 50
	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(i int) {
			defer wg.Done()
			var ipToQuery string
			var expectedOp int
			if i%2 == 0 {
				ipToQuery = "*******00"
				expectedOp = Mobile
			} else {
				ipToQuery = "*********"
				expectedOp = Telecom
			}
			op, qErr := Query(ipToQuery)
			if qErr != nil {
				t.Errorf("Goroutine %d: Query(%s) unexpected error: %v", i, ipToQuery, qErr)
				return
			}
			if op != expectedOp {
				t.Errorf("Goroutine %d: Query(%s) expected %d, got %d", i, ipToQuery, expectedOp, op)
			}
		}(i)
	}
	wg.Wait()
}


// contains checks if a string contains a substring.
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

// Helper needed for TestIPDB_Query_BeforeInit and TestIPDB_Init_Failures to make sure initErr is actually set by Init.
// The global variable initErr is what Query checks.
var _ = initErr // Use initErr to satisfy the linter if not used elsewhere in this file directly in a check