package ipdb

import (
	"bufio"
	"bytes"
	"fmt"
	"net"
	"os"
	"strings"
	"sync"
)

// Operator IDs
const (
	UnknownOperator = 0
	Unicom          = 1 // 联通
	Mobile          = 2 // 移动
	Telecom         = 3 // 电信
)

// OperatorNameMap maps Chinese operator names to their IDs
var OperatorNameMap = map[string]int{
	"联通": Unicom,
	"移动": Mobile,
	"电信": Telecom,
}

// IPRange represents a single IP range with its operator
type IPRange struct {
	StartIP    net.IP
	EndIP      net.IP
	OperatorID int
}

var (
	ipv4Ranges []IPRange
	ipv6Ranges []IPRange
	once       sync.Once
	initErr    error
	mu         sync.RWMutex // To protect access to ipv4Ranges and ipv6Ranges
)

// Init loads IP ranges from the specified files.
// It should be called once during application startup.
// File format is assumed to be: START_IP END_IP OPERATOR_NAME (e.g., "******* ********* 电信")
func Init(ipv4FilePath, ipv6FilePath string) error {
	once.Do(func() {
		mu.Lock()
		defer mu.Unlock()

		var err error
		ipv4Ranges, err = loadRangesFromFile(ipv4FilePath, false)
		if err != nil {
			initErr = fmt.Errorf("failed to load IPv4 ranges from %s: %w", ipv4FilePath, err)
			return
		}

		ipv6Ranges, err = loadRangesFromFile(ipv6FilePath, true)
		if err != nil {
			initErr = fmt.Errorf("failed to load IPv6 ranges from %s: %w", ipv6FilePath, err)
			return
		}
	})
	return initErr
}

func loadRangesFromFile(filePath string, isIPv6File bool) ([]IPRange, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("opening file %s: %w", filePath, err)
	}
	defer file.Close()

	var ranges []IPRange
	scanner := bufio.NewScanner(file)
	lineNumber := 0
	for scanner.Scan() {
		lineNumber++
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") { // Skip empty lines or comments
			continue
		}

		// 按空格分割行
		parts := strings.Fields(line)
		
		// 检查字段数量是否正确（必须恰好是3个字段：起始IP、结束IP和运营商名称）
		if len(parts) != 3 {
			return nil, fmt.Errorf("file %s, line %d: invalid format, expected 3 fields (start_ip end_ip operator_name), got %d fields: '%s'", filePath, lineNumber, len(parts), line)
		}

		// 提取起始IP、结束IP和运营商名称
		startIPStr, endIPStr, operatorName := parts[0], parts[1], parts[2]

		startIP := net.ParseIP(startIPStr)
		if startIP == nil {
			return nil, fmt.Errorf("file %s, line %d: invalid start IP '%s'", filePath, lineNumber, startIPStr)
		}

		endIP := net.ParseIP(endIPStr)
		if endIP == nil {
			return nil, fmt.Errorf("file %s, line %d: invalid end IP '%s'", filePath, lineNumber, endIPStr)
		}

		// Standardize IP representation
		if isIPv6File {
			// For IPv6 file, ensure IPs are in 16-byte form. If it's an IPv4-mapped IPv6, it's fine.
			if startIP.To4() != nil { // It's an IPv4 or IPv4-mapped IPv6 address
				// If strict IPv6 is required in IPv6 file, this could be an error.
				// For now, we allow IPv4-mapped addresses and convert them to full IPv6 form if necessary.
			}
			startIP = startIP.To16()
			endIP = endIP.To16()
			if startIP == nil || endIP == nil { // Should not happen if ParseIP succeeded
				return nil, fmt.Errorf("file %s, line %d: failed to convert IP to 16-byte form for '%s' or '%s'", filePath, lineNumber, startIPStr, endIPStr)
			}
		} else { // IPv4 file
			// For IPv4 file, ensure IPs are in 4-byte form.
			// net.ParseIP might return IPv4-mapped IPv6 for IPv4 strings, so convert to 4-byte.
			startIP = startIP.To4()
			endIP = endIP.To4()
			if startIP == nil || endIP == nil {
				return nil, fmt.Errorf("file %s, line %d: not a valid IPv4 address '%s' or '%s' (or failed To4 conversion)", filePath, lineNumber, startIPStr, endIPStr)
			}
		}

		operatorID, ok := OperatorNameMap[operatorName]
		if !ok {
			return nil, fmt.Errorf("file %s, line %d: unknown operator name '%s'", filePath, lineNumber, operatorName)
		}

		if bytes.Compare(startIP, endIP) > 0 {
			return nil, fmt.Errorf("file %s, line %d: start IP %s is greater than end IP %s", filePath, lineNumber, startIPStr, endIPStr)
		}

		ranges = append(ranges, IPRange{
			StartIP:    startIP,
			EndIP:      endIP,
			OperatorID: operatorID,
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("reading file %s: %w", filePath, err)
	}

	return ranges, nil
}

// Query takes an IP string, parses it, and returns the OperatorID if found.
// Returns UnknownOperator and nil error if IP is valid but not found in ranges.
// Returns an error if IP string is invalid or if the module was not initialized successfully.
func Query(ipStr string) (int, error) {
	mu.RLock()
	defer mu.RUnlock()

	if initErr != nil {
		return UnknownOperator, fmt.Errorf("IPDB not initialized: %w", initErr)
	}

	ip := net.ParseIP(ipStr)
	if ip == nil {
		return UnknownOperator, fmt.Errorf("invalid IP address format: %s", ipStr)
	}

	var targetRanges []IPRange
	ipV4 := ip.To4()

	if ipV4 != nil {
		ip = ipV4 // Use 4-byte representation for IPv4
		targetRanges = ipv4Ranges
	} else {
		ip = ip.To16() // Use 16-byte representation for IPv6
		targetRanges = ipv6Ranges
	}

	for _, r := range targetRanges {
		// Check if ip >= r.StartIP and ip <= r.EndIP
		if bytes.Compare(ip, r.StartIP) >= 0 && bytes.Compare(ip, r.EndIP) <= 0 {
			return r.OperatorID, nil
		}
	}

	return UnknownOperator, nil // IP valid, but not in any defined range
}
