#!/usr/bin/env python3
import etcd
import json
import sys
import datetime

# --- 配置 ---
ETCD_HOST = '*************'
ETCD_PORT = 2379
ETCD_PROTOCOL = 'http' # v2 通常使用 http
ETCD_PREFIX = '/nodes/' # 与 Go 脚本中的前缀一致

# --- 连接到 etcd ---
try:
    client = etcd.Client(host=ETCD_HOST, port=ETCD_PORT, protocol=ETCD_PROTOCOL, allow_reconnect=True)
    # 尝试简单读取根目录以测试连接
    client.read('/')
    print(f"Successfully connected to etcd at {ETCD_PROTOCOL}://{ETCD_HOST}:{ETCD_PORT}")
except etcd.EtcdConnectionFailed as e:
    print(f"Error: Could not connect to etcd at {ETCD_PROTOCOL}://{ETCD_HOST}:{ETCD_PORT}. Is etcd running?", file=sys.stderr)
    print(f"Details: {e}", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"An unexpected error occurred during connection: {e}", file=sys.stderr)
    sys.exit(1)


# --- 查询数据 ---
print(f"\nQuerying keys under prefix: {ETCD_PREFIX}")
try:
    # 读取指定前缀下的所有键值 (recursive=True 获取目录下的所有内容)
    result = client.read(ETCD_PREFIX, recursive=True)
except etcd.EtcdKeyNotFound:
    print(f"No keys found under prefix '{ETCD_PREFIX}'.")
    sys.exit(0)
except Exception as e:
    print(f"An error occurred while reading from etcd: {e}", file=sys.stderr)
    sys.exit(1)

# --- 处理和打印结果 ---
node_count = 0
if result and result.leaves: # leaves 包含所有非目录节点
    for node in result.leaves:
        node_count += 1
        print("-" * 40)
        print(f"Key: {node.key}")

        # 尝试解析 JSON 值
        try:
            value_data = json.loads(node.value)
            print("Value (JSON):")
            # 使用 json.dumps 美化输出
            print(json.dumps(value_data, indent=2))
        except json.JSONDecodeError:
            print(f"Value (Raw): {node.value}")
            print("(Value is not valid JSON)")

        # 打印 TTL 信息
        if node.ttl is not None:
            expiration_time = datetime.datetime.now() + datetime.timedelta(seconds=node.ttl)
            print(f"TTL: {node.ttl} seconds (Expires around: {expiration_time.strftime('%Y-%m-%d %H:%M:%S')})")
        else:
            print("TTL: Not set")

        # 打印其他元数据 (可选)
        # print(f"Created Index: {node.createdIndex}")
        # print(f"Modified Index: {node.modifiedIndex}")

else:
    print(f"No leaf nodes found under prefix '{ETCD_PREFIX}'.")

print("-" * 40)
print(f"\nFound {node_count} node(s) under '{ETCD_PREFIX}'.")
