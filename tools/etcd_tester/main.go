package main

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"funshion.com/upload_tuner/internal/models"
	"go.etcd.io/etcd/client/v2"
)

// customHeaderTransport 是一个自定义的 HTTP 传输，用于添加 API 密钥到请求头
type customHeaderTransport struct {
	Transport http.RoundTripper
	ApiKey    string
}

// RoundTrip 实现 http.RoundTripper 接口
func (t *customHeaderTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.Header.Add("API-Key", t.Api<PERSON>ey)
	return t.Transport.RoundTrip(req)
}

// CancelRequest 实现 client.CancelableTransport 接口
func (t *customHeaderTransport) CancelRequest(req *http.Request) {
	// 如果底层传输支持取消请求，则调用它的 CancelRequest 方法
	if ct, ok := t.Transport.(client.CancelableTransport); ok {
		ct.CancelRequest(req)
	}
}

var (
	etcdProxy   = flag.String("proxy", "http://*************:2379", "Etcd address")
	nodePrefix  = flag.String("prefix", "/nodes/", "Node key prefix in etcd")
	testNodeID  = flag.String("node", "test-node", "Test node ID prefix")
	count       = flag.Int("count", 3, "Number of test nodes to create")
	delay       = flag.Int("delay", 5, "Delay between operations in seconds")
	ttl         = flag.Int("ttl", 30, "TTL for node keys in seconds")
	interactive = flag.Bool("interactive", false, "Run in interactive mode")
	apiKey      = flag.String("apikey", "_Tn2F-Kp*UHaVx9=", "API key for Nginx proxy")
)

func main() {
	flag.Parse()

	log.Printf("Etcd 测试工具启动")
	log.Printf("将通过代理连接到 etcd, 代理地址: %s", *etcdProxy)

	// 初始化 etcd 客户端
	// 创建带有 API 密钥的自定义传输
	customTransport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		Proxy:           http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	// 创建自定义的 HTTP 客户端
	customClient := &http.Client{
		Transport: customTransport,
		Timeout:   time.Second * 30,
	}

	// 创建带有 API 密钥的传输
	customHeaderTransport := &customHeaderTransport{
		Transport: customClient.Transport,
		ApiKey:    *apiKey,
	}

	cfg := client.Config{
		Endpoints: []string{*etcdProxy},
		Transport: customHeaderTransport,
		// 设置超时
		HeaderTimeoutPerRequest: time.Second * 10,
	}

	c, err := client.New(cfg)
	if err != nil {
		log.Fatalf("创建 etcd 客户端失败: %v", err)
	}

	kapi := client.NewKeysAPI(c)
	log.Printf("已初始化 etcd v2 client, 使用代理: %s", *etcdProxy)

	// 捕获退出信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	// 设置一个函数，在程序退出前执行清理操作
	defer func() {
		signal.Stop(sigCh) // 停止接收信号
		log.Println("程序已退出")
	}()

	if *interactive {
		// 在交互模式下处理 Ctrl+C
		go func() {
			<-sigCh
			log.Println("\n收到中断信号，正在退出交互模式...")
			os.Exit(0)
		}()
		runInteractiveMode(kapi)
	} else {
		runAutomatedTests(kapi, sigCh)
	}
}

func runInteractiveMode(kapi client.KeysAPI) {
	log.Println("交互模式启动。输入命令:")
	log.Println("1: 添加测试节点")
	log.Println("2: 更新测试节点")
	log.Println("3: 删除测试节点")
	log.Println("4: 列出所有节点")
	log.Println("q: 退出")

	// 创建上下文，用于控制操作的取消
	_, cancel := context.WithCancel(context.Background())
	defer cancel()

	var input string
	for {
		fmt.Print("> ")
		fmt.Scanln(&input)

		switch input {
		case "1":
			addTestNode(kapi, *testNodeID+"-"+strconv.Itoa(rand.Intn(1000)))
		case "2":
			updateTestNode(kapi, *testNodeID+"-"+strconv.Itoa(rand.Intn(1000)))
		case "3":
			deleteTestNode(kapi, *testNodeID+"-"+strconv.Itoa(rand.Intn(1000)))
		case "4":
			listAllNodes(kapi)
		case "q":
			log.Println("退出交互模式")
			return
		default:
			log.Println("未知命令")
		}
	}
}

func runAutomatedTests(kapi client.KeysAPI, sigCh chan os.Signal) {
	// 创建上下文，用于控制操作的取消
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建一个通道用于通知主程序测试完成
	done := make(chan struct{})

	// 在单独的 goroutine 中处理信号
	go func() {
		<-sigCh
		log.Println("\n收到中断信号，正在优雅退出...")
		cancel() // 取消上下文
	}()

	// 在单独的 goroutine 中运行测试
	go func() {
		defer close(done)

		// 创建测试节点
		nodeIDs := make([]string, *count)
		for i := 0; i < *count; i++ {
			select {
			case <-ctx.Done():
				log.Println("节点创建阶段被中断")
				return
			default:
				nodeID := fmt.Sprintf("%s-%d", *testNodeID, i+1)
				nodeIDs[i] = nodeID
				addTestNode(kapi, nodeID)

				// 检查是否需要继续
				if i < *count-1 {
					select {
					case <-ctx.Done():
						log.Println("节点创建阶段被中断")
						return
					case <-time.After(time.Duration(*delay) * time.Second):
						// 继续下一个节点
					}
				}
			}
		}

		// 等待一段时间后更新节点
		select {
		case <-ctx.Done():
			log.Println("更新节点阶段被中断")
			return
		case <-time.After(time.Duration(*delay) * time.Second):
			log.Printf("等待 %d 秒后更新节点...", *delay)
		}

		// 更新测试节点
		for i, nodeID := range nodeIDs {
			select {
			case <-ctx.Done():
				log.Println("更新节点阶段被中断")
				return
			default:
				updateTestNode(kapi, nodeID)

				// 检查是否需要继续
				if i < len(nodeIDs)-1 {
					select {
					case <-ctx.Done():
						log.Println("更新节点阶段被中断")
						return
					case <-time.After(time.Duration(*delay) * time.Second):
						// 继续下一个节点
					}
				}
			}
		}

		// 等待一段时间后删除节点
		select {
		case <-ctx.Done():
			log.Println("删除节点阶段被中断")
			return
		case <-time.After(time.Duration(*delay) * time.Second):
			log.Printf("等待 %d 秒后删除节点...", *delay)
		}

		// 删除测试节点
		for i, nodeID := range nodeIDs {
			select {
			case <-ctx.Done():
				log.Println("删除节点阶段被中断")
				return
			default:
				deleteTestNode(kapi, nodeID)

				// 检查是否需要继续
				if i < len(nodeIDs)-1 {
					select {
					case <-ctx.Done():
						log.Println("删除节点阶段被中断")
						return
					case <-time.After(time.Duration(*delay) * time.Second):
						// 继续下一个节点
					}
				}
			}
		}

		log.Println("测试完成")
	}()

	// 等待测试完成或被中断
	select {
	case <-done:
		log.Println("所有测试已完成，程序退出")
	case <-ctx.Done():
		log.Println("程序被中断，等待清理操作完成...")
		// 给清理操作一些时间
		time.Sleep(1 * time.Second)
	}
}

func addTestNode(kapi client.KeysAPI, nodeID string) {
	log.Printf("添加测试节点: %s", nodeID)

	// 创建节点数据
	node := models.Node{
		ID:       nodeID,
		IP:       fmt.Sprintf("192.168.100.%d", rand.Intn(254)+1),
		Port:     8080,
		Capacity: 100,
	}

	nodeJSON, err := json.Marshal(node)
	if err != nil {
		log.Printf("序列化节点数据失败: %v", err)
		return
	}

	// 设置 TTL 选项
	opts := &client.SetOptions{
		TTL: time.Duration(*ttl) * time.Second,
	}

	// 写入 etcd
	key := *nodePrefix + nodeID
	_, err = kapi.Set(context.Background(), key, string(nodeJSON), opts)
	if err != nil {
		log.Printf("添加节点失败: %v", err)
		return
	}

	log.Printf("成功添加节点 %s: %s", nodeID, string(nodeJSON))
}

func updateTestNode(kapi client.KeysAPI, nodeID string) {
	log.Printf("更新测试节点: %s", nodeID)

	// 先检查节点是否存在
	key := *nodePrefix + nodeID
	resp, err := kapi.Get(context.Background(), key, nil)
	if err != nil {
		log.Printf("获取节点失败: %v", err)
		return
	}

	// 解析现有节点数据
	var node models.Node
	if err := json.Unmarshal([]byte(resp.Node.Value), &node); err != nil {
		log.Printf("解析节点数据失败: %v", err)
		return
	}

	// 更新节点数据
	// 只更新我们可以更新的字段
	node.Capacity = rand.Intn(200) + 50 // 50-250 容量

	nodeJSON, err := json.Marshal(node)
	if err != nil {
		log.Printf("序列化节点数据失败: %v", err)
		return
	}

	// 设置 TTL 选项
	opts := &client.SetOptions{
		TTL: time.Duration(*ttl) * time.Second,
	}

	// 更新 etcd
	_, err = kapi.Set(context.Background(), key, string(nodeJSON), opts)
	if err != nil {
		log.Printf("更新节点失败: %v", err)
		return
	}

	log.Printf("成功更新节点 %s: %s", nodeID, string(nodeJSON))
}

func deleteTestNode(kapi client.KeysAPI, nodeID string) {
	log.Printf("删除测试节点: %s", nodeID)

	key := *nodePrefix + nodeID
	_, err := kapi.Delete(context.Background(), key, nil)
	if err != nil {
		log.Printf("删除节点失败: %v", err)
		return
	}

	log.Printf("成功删除节点 %s", nodeID)
}

func listAllNodes(kapi client.KeysAPI) {
	log.Printf("列出所有节点")

	resp, err := kapi.Get(context.Background(), *nodePrefix, &client.GetOptions{Recursive: true})
	if err != nil {
		log.Printf("获取节点列表失败: %v", err)
		return
	}

	if !resp.Node.Dir {
		log.Printf("节点前缀不是目录")
		return
	}

	log.Printf("发现 %d 个节点:", len(resp.Node.Nodes))
	for _, node := range resp.Node.Nodes {
		log.Printf("  - %s: %s", node.Key, node.Value)
	}
}
