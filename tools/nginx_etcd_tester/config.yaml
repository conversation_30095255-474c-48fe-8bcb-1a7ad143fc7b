# etcd 节点注册和心跳维护工具配置

# 网络配置
# nginx_proxy_endpoint: "http://192.168.16.61:8080"  # Nginx 代理端点
nginx_proxy_endpoint: "http://192.168.16.122:80"  # Nginx 代理端点
# etcd_endpoints: "http://192.168.16.61:2379"        # 直连 etcd 的备用地址
dial_timeout: 5                                    # 连接超时时间（秒）
api_key: "_Tn2F-Kp*UHaVx9="                      # Nginx 代理所需的 API 密钥
use_nginx_proxy: true                              # 是否使用 Nginx 代理

# 节点数据配置
etcd_prefix: "/nodes/"                             # 节点数据的键前缀
ttl_duration: 30                                   # TTL: 键存活时间（秒）
heartbeat_interval: 10                             # 心跳间隔（秒）

# 测试节点数据
test_nodes:
  - id: "node-2010"
    ip: "*************"
    port: 8081
    capacity: 100
  - id: "node-2020"
    ip: "*************"
    port: 8081
    capacity: 150
  - id: "node-2030"
    ip: "*************"
    port: 8081
    capacity: 200
