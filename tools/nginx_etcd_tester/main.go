package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"sync"
	"syscall"
	"time"

	"funshion.com/upload_tuner/internal/models"

	// 使用 etcd v2 client
	etcdClient "github.com/coreos/etcd/client"
	"gopkg.in/yaml.v2" // YAML 解析库
)

// customHeaderTransport 是一个自定义的HTTP传输层，用于添加API密钥头
// 实现 etcdClient.CancelableTransport 接口
// 这允许我们在每个请求中添加自定义头部
// 例如API密钥验证
// 这对于与需要认证的Nginx代理通信是必要的
type customHeaderTransport struct {
	Transport http.RoundTripper
	Headers   http.Header
}

// RoundTrip 实现http.RoundTripper接口
// 在每个请求中添加自定义头部
func (t *customHeaderTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	// 添加所有自定义头部
	for k, vv := range t.Headers {
		for _, v := range vv {
			req.Header.Add(k, v)
		}
	}
	// 使用底层传输层发送请求
	return t.Transport.RoundTrip(req)
}

// CancelRequest 实现 etcdClient.CancelableTransport 接口
// 用于取消请求
func (t *customHeaderTransport) CancelRequest(req *http.Request) {
	// 如果底层传输层支持取消请求，则调用其CancelRequest方法
	if tr, ok := t.Transport.(etcdClient.CancelableTransport); ok {
		tr.CancelRequest(req)
	}
}

// Config 存储应用程序配置
type Config struct {
	NginxProxyEndpoint string        `yaml:"nginx_proxy_endpoint"` // Nginx 代理端点
	DialTimeout        time.Duration `yaml:"dial_timeout"`        // 连接超时时间（秒）
	EtcdPrefix         string        `yaml:"etcd_prefix"`         // 节点数据的键前缀
	TTLDuration        time.Duration `yaml:"ttl_duration"`        // TTL: 键存活时间（秒）
	HeartbeatInterval  time.Duration `yaml:"heartbeat_interval"`  // 心跳间隔（秒）
	APIKey             string        `yaml:"api_key"`             // Nginx 代理所需的 API 密钥
	TestNodes          []models.Node `yaml:"test_nodes"`          // 测试节点数据
}

// 默认配置
var defaultConfig = Config{
	NginxProxyEndpoint: "http://localhost:8080",
	DialTimeout:        5 * time.Second,
	EtcdPrefix:         "/nodes/",
	TTLDuration:        30 * time.Second,
	HeartbeatInterval:  10 * time.Second,
	APIKey:             "secret_key_123",
}

// 全局配置变量
var config Config

// 加载配置文件
func loadConfig(configPath string) error {
	// 首先使用默认配置
	config = defaultConfig

	// 如果没有指定配置文件路径，尝试使用默认路径
	if configPath == "" {
		// 默认配置文件路径
		defaultPaths := []string{
			"./config.yaml",
			"./config.yml",
			"../config/seed_etcd.yaml",
			"../../configs/seed_etcd.yaml",
		}

		// 尝试每个默认路径
		for _, path := range defaultPaths {
			if _, err := os.Stat(path); err == nil {
				configPath = path
				log.Printf("使用默认配置文件: %s", configPath)
				break
			}
		}

		// 如果所有默认路径都不存在，使用内置默认配置
		if configPath == "" {
			log.Println("未找到配置文件，使用内置默认配置")
			return nil
		}
	}

	// 读取配置文件
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 根据文件扩展名选择解析方式
	ext := filepath.Ext(configPath)
	switch ext {
	case ".yaml", ".yml":
		// 解析 YAML 配置
		if err := yaml.Unmarshal(data, &config); err != nil {
			return fmt.Errorf("解析 YAML 配置文件失败: %v", err)
		}
	case ".json":
		// 解析 JSON 配置
		if err := json.Unmarshal(data, &config); err != nil {
			return fmt.Errorf("解析 JSON 配置文件失败: %v", err)
		}
	default:
		return fmt.Errorf("不支持的配置文件格式: %s", ext)
	}

	// 确保时间单位正确（配置文件中以秒为单位，需要转换为时间对象）
	if config.DialTimeout < time.Second {
		config.DialTimeout *= time.Second
	}
	if config.TTLDuration < time.Second {
		config.TTLDuration *= time.Second
	}
	if config.HeartbeatInterval < time.Second {
		config.HeartbeatInterval *= time.Second
	}

	log.Printf("已加载配置文件: %s", configPath)
	return nil
}

// maintainNodePresence 在后台为单个节点维持心跳
func maintainNodePresence(wg *sync.WaitGroup, shutdown chan struct{}, node models.Node) {
	defer wg.Done()
	// 使用节点ID作为键名
	key := config.EtcdPrefix + node.ID
	
	// 将节点信息序列化为JSON
	nodeJSON, err := json.Marshal(node)
	if err != nil {
		log.Printf("[Node %s] Error serializing for heartbeat: %v", node.ID, err)
		return // 无法序列化，无法继续
	}
	value := string(nodeJSON)
	
	// 设置TTL选项
	setOptions := &etcdClient.SetOptions{TTL: config.TTLDuration}
	
	// 创建定时器用于心跳
	ticker := time.NewTicker(config.HeartbeatInterval)
	defer ticker.Stop()

	log.Printf("[Node %s] Starting heartbeat maintenance (key: %s, TTL: %v, Interval: %v)", node.ID, key, config.TTLDuration, config.HeartbeatInterval)

	// --- 初始注册 ---
	ctxInitial, cancelInitial := context.WithTimeout(context.Background(), config.DialTimeout)
	// 确保在函数返回前调用cancelInitial以避免上下文泄漏
	defer cancelInitial()
	
	// 使用自定义头部的HTTP请求调用Nginx代理
	headers := make(http.Header)
	headers.Set("API-Key", config.APIKey)
	
	// 设置自定义传输
	customTransport := etcdClient.DefaultTransport.(*http.Transport).Clone()
	
	// 使用自定义头部创建 etcd 客户端
	customCfg := etcdClient.Config{
		Endpoints: []string{config.NginxProxyEndpoint},
		Transport: &customHeaderTransport{
			Transport: customTransport,
			Headers:   headers,
		},
		HeaderTimeoutPerRequest: config.DialTimeout,
	}
	
	// 使用自定义配置创建新的etcd客户端
	customC, err := etcdClient.New(customCfg)
	if err != nil {
		log.Printf("[Node %s] Failed to create custom etcd client: %v", node.ID, err)
		return
	}
	
	// 使用自定义客户端创建 KeysAPI
	customKapi := etcdClient.NewKeysAPI(customC)
	
	// 使用自定义KeysAPI设置值
	_, err = customKapi.Set(ctxInitial, key, value, setOptions)
	
	if err != nil {
		log.Printf("[Node %s] Initial registration failed (key: %s): %v. Aborting heartbeat.", node.ID, key, err)
		return // 初始设置失败，则不进行心跳
	}
	
	fmt.Printf("[Node %s] Successfully registered with TTL (key: %s)\n", node.ID, key)

	// --- 心跳循环 ---
	for {
		select {
		case <-ticker.C:
			// 发送心跳 (通过带 TTL 的 Set 更新)
			ctxHeartbeat, cancelHeartbeat := context.WithTimeout(context.Background(), config.DialTimeout)
			
			// 使用自定义头部的HTTP请求调用Nginx代理
			headers := make(http.Header)
			headers.Set("API-Key", config.APIKey)
			
			// 设置自定义传输
			customTransport := etcdClient.DefaultTransport.(*http.Transport).Clone()
			
			// 使用自定义头部创建 etcd 客户端
			customCfg := etcdClient.Config{
				Endpoints: []string{config.NginxProxyEndpoint},
				Transport: &customHeaderTransport{
					Transport: customTransport,
					Headers:   headers,
				},
				HeaderTimeoutPerRequest: config.DialTimeout,
			}
			
			// 使用自定义配置创建新的etcd客户端
			customC, err := etcdClient.New(customCfg)
			if err != nil {
				log.Printf("[Node %s] Failed to create custom etcd client for heartbeat: %v", node.ID, err)
				cancelHeartbeat()
				continue
			}
			
			// 使用自定义客户端创建 KeysAPI
			customKapi := etcdClient.NewKeysAPI(customC)
			
			// 使用自定义KeysAPI设置值
			_, err = customKapi.Set(ctxHeartbeat, key, value, setOptions)
			
			cancelHeartbeat()
			
			if err != nil {
				// 如果 key 不存在（例如被手动删除），Set 会重新创建它
				log.Printf("[Node %s] Heartbeat update/set failed (key: %s): %v", node.ID, key, err)
				// 可以根据错误类型决定是否重试或退出
			} else {
				// log.Printf("[Node %s] Heartbeat successful (key: %s)", node.ID, key) // 可以取消注释以查看每次心跳日志
			}
		case <-shutdown:
			log.Printf("[Node %s] Shutdown signal received. Stopping heartbeat (key: %s). Key will expire after TTL.", node.ID, key)
			// 可选：在退出前删除 key
			// ctxDelete, cancelDelete := context.WithTimeout(context.Background(), config.DialTimeout)
			// _, _ = kapi.Delete(ctxDelete, key, nil)
			// cancelDelete()
			// log.Printf("[Node %s] Deleted key %s on shutdown.", node.ID, key)
			return // 退出 goroutine
		}
	}
}

func main() {
	// 定义命令行参数
	configPath := flag.String("config", "", "配置文件路径")
	flag.Parse()

	// 加载配置文件
	if err := loadConfig(*configPath); err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}
	
	// 使用配置文件中的测试节点数据，如果没有则使用默认测试节点
	testNodes := config.TestNodes
	if len(testNodes) == 0 {
		testNodes = []models.Node{
			{ID: "node-2010", IP: "*************", Port: 8081, Capacity: 100},
			{ID: "node-2020", IP: "*************", Port: 8081, Capacity: 100},
			{ID: "node-2030", IP: "*************", Port: 8081, Capacity: 100},
		}
		log.Println("使用默认测试节点数据")
	}

	// --- 通过 Nginx 代理连接到 etcd ---
	log.Printf("将通过 Nginx 代理连接到 etcd, 代理地址: %s", config.NginxProxyEndpoint)
	
	// 创建自定义头部
	headers := make(http.Header)
	headers.Set("API-Key", config.APIKey)
	
	// 设置自定义传输
	customTransport := etcdClient.DefaultTransport.(*http.Transport).Clone()
	
	// 使用自定义头部创建 etcd 客户端配置
	customCfg := etcdClient.Config{
		Endpoints: []string{config.NginxProxyEndpoint},
		Transport: &customHeaderTransport{
			Transport: customTransport,
			Headers:   headers,
		},
		HeaderTimeoutPerRequest: config.DialTimeout,
	}
	
	// 创建 etcd 客户端
	_, err := etcdClient.New(customCfg)
	if err != nil {
		log.Fatalf("无法创建通过 Nginx 代理的 etcd 客户端: %v", err)
	}
	
	// 创建 KeysAPI
	log.Printf("已初始化 etcd v2 client, 使用代理: true")

	// --- 启动心跳维护 ---
	var wg sync.WaitGroup
	shutdown := make(chan struct{}) // 用于通知 goroutine 退出的 channel

	log.Println("开始节点注册和心跳维护...")
	for i := range testNodes { // 使用索引以确保 goroutine 捕获正确的 node
		wg.Add(1)
		go maintainNodePresence(&wg, shutdown, testNodes[i])
	}

	// --- 等待退出信号 ---
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM) // 监听 Ctrl+C 和 kill 命令
	log.Printf("心跳工具运行中. 按 Ctrl+C 停止...")

	<-sigChan // 阻塞，直到接收到信号

	// --- 开始优雅关闭 ---
	log.Println("\n收到关闭信号. 正在优雅停止心跳...")
	close(shutdown) // 向所有 goroutine 发送停止信号

	// 等待所有心跳 goroutine 结束
	log.Println("等待所有心跳线程完成...")
	wg.Wait() // 等待所有 wg.Done() 被调用

	log.Println("所有心跳已停止. 程序退出.")
}
