#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
脚本: 向 etcd 中写入应用密钥
用法: python3 add_app_secret.py --app-id <应用ID> --secret <密钥> [--etcd-endpoint <etcd端点>]
"""

import json
import argparse
import urllib.request
import urllib.error
import urllib.parse
import hashlib
import sys
import time
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# etcd 应用密钥前缀
ETCD_APP_SECRET_PREFIX = "/app_secrets/"

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='向 etcd 中写入应用密钥')
    parser.add_argument('--app-id', required=True, help='应用ID')
    parser.add_argument('--secret', required=True, help='应用密钥')
    parser.add_argument('--etcd-endpoint', default='http://*************:2379', help='etcd 端点地址')
    parser.add_argument('--test', action='store_true', help='测试密钥是否有效')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细输出')
    return parser.parse_args()

def write_app_secret(etcd_endpoint, app_id, secret, verbose=False):
    """向 etcd 中写入应用密钥"""
    # 构建完整的 etcd 键路径
    key_path = f"{ETCD_APP_SECRET_PREFIX}{app_id}"
    
    # 构建应用密钥数据
    app_secret_data = {
        "app_id": app_id,
        "secret": secret
    }
    
    # 将数据转换为 JSON 字符串
    value = json.dumps(app_secret_data)
    
    # 构建 etcd API URL
    url = f"{etcd_endpoint}/v2/keys{key_path}"
    
    if verbose:
        logger.info(f"写入应用密钥到 etcd: {url}")
        logger.info(f"应用密钥数据: {value}")
    
    try:
        # 准备请求数据
        data = urllib.parse.urlencode({"value": value}).encode('utf-8')
        
        # 创建请求
        req = urllib.request.Request(url, data=data, method='PUT')
        
        # 发送请求
        with urllib.request.urlopen(req) as response:
            # 读取响应
            response_data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            # 检查响应状态
            if status_code == 200 or status_code == 201:
                logger.info(f"成功写入应用密钥: {app_id}")
                return True
            else:
                logger.error(f"写入应用密钥失败: {status_code} - {response_data}")
                return False
    
    except urllib.error.HTTPError as e:
        logger.error(f"写入应用密钥失败: {e.code} - {e.reason}")
        try:
            error_data = e.read().decode('utf-8')
            logger.error(f"错误详情: {error_data}")
        except:
            pass
        return False
    
    except Exception as e:
        logger.error(f"写入应用密钥时发生错误: {str(e)}")
        return False

def read_app_secret(etcd_endpoint, app_id, verbose=False):
    """从 etcd 中读取应用密钥"""
    # 构建完整的 etcd 键路径
    key_path = f"{ETCD_APP_SECRET_PREFIX}{app_id}"
    
    # 构建 etcd API URL
    url = f"{etcd_endpoint}/v2/keys{key_path}"
    
    if verbose:
        logger.info(f"从 etcd 读取应用密钥: {url}")
    
    try:
        # 创建请求
        req = urllib.request.Request(url)
        
        # 发送请求
        with urllib.request.urlopen(req) as response:
            # 读取响应
            response_data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            # 检查响应状态
            if status_code == 200:
                data = json.loads(response_data)
                if "node" in data and "value" in data["node"]:
                    value = data["node"]["value"]
                    app_secret = json.loads(value)
                    if verbose:
                        logger.info(f"读取到应用密钥: {app_secret}")
                    return app_secret
                else:
                    logger.error(f"应用密钥数据格式错误: {data}")
                    return None
            else:
                logger.error(f"读取应用密钥失败: {status_code} - {response_data}")
                return None
    
    except urllib.error.HTTPError as e:
        if e.code == 404:
            logger.warning(f"应用密钥不存在: {app_id}")
            return None
        else:
            logger.error(f"读取应用密钥失败: {e.code} - {e.reason}")
            try:
                error_data = e.read().decode('utf-8')
                logger.error(f"错误详情: {error_data}")
            except:
                pass
            return None
    
    except Exception as e:
        logger.error(f"读取应用密钥时发生错误: {str(e)}")
        return None

def test_app_secret(app_id, secret, verbose=False):
    """测试应用密钥是否有效"""
    # 生成当前时间戳
    timestamp = str(int(time.time()))
    
    # 生成令牌
    content = f"{secret}|{app_id}|{timestamp}"
    token = hashlib.md5(content.encode('utf-8')).hexdigest()
    
    if verbose:
        logger.info(f"测试应用密钥: app_id={app_id}, timestamp={timestamp}")
        logger.info(f"生成令牌: {token}")
        logger.info(f"令牌生成内容: {content}")
    
    logger.info(f"应用密钥测试成功")
    logger.info(f"应用ID: {app_id}")
    logger.info(f"密钥: {secret}")
    logger.info(f"测试时间戳: {timestamp}")
    logger.info(f"生成的令牌: {token}")
    logger.info(f"API 调用示例:")
    logger.info(f"  curl -X POST 'http://localhost:8081/app/node/{app_id}?token={token}&ts={timestamp}&cip=*************'")
    
    return True

def list_all_app_secrets(etcd_endpoint, verbose=False):
    """列出所有应用密钥"""
    # 构建 etcd API URL
    url = f"{etcd_endpoint}/v2/keys{ETCD_APP_SECRET_PREFIX}?recursive=true"
    
    if verbose:
        logger.info(f"列出所有应用密钥: {url}")
    
    try:
        # 创建请求
        req = urllib.request.Request(url)
        
        # 发送请求
        with urllib.request.urlopen(req) as response:
            # 读取响应
            response_data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            # 检查响应状态
            if status_code == 200:
                data = json.loads(response_data)
                if "node" in data and "nodes" in data["node"]:
                    logger.info("已配置的应用密钥列表:")
                    for node in data["node"]["nodes"]:
                        if "value" in node:
                            try:
                                app_secret = json.loads(node["value"])
                                app_id = app_secret.get("app_id", "未知")
                                secret = app_secret.get("secret", "未知")
                                logger.info(f"  应用ID: {app_id}, 密钥: {secret}")
                            except:
                                logger.warning(f"  无法解析应用密钥: {node['value']}")
                    return True
                else:
                    logger.info("没有找到应用密钥")
                    return True
            else:
                logger.error(f"列出应用密钥失败: {status_code} - {response_data}")
                return False
    
    except urllib.error.HTTPError as e:
        if e.code == 404:
            logger.info("没有找到应用密钥")
            return True
        else:
            logger.error(f"列出应用密钥失败: {e.code} - {e.reason}")
            try:
                error_data = e.read().decode('utf-8')
                logger.error(f"错误详情: {error_data}")
            except:
                pass
            return False
    
    except Exception as e:
        logger.error(f"列出应用密钥时发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    args = parse_args()
    
    # 如果没有提供应用ID，则列出所有应用密钥
    if args.app_id == "list":
        return list_all_app_secrets(args.etcd_endpoint, args.verbose)
    
    # 写入应用密钥
    success = write_app_secret(args.etcd_endpoint, args.app_id, args.secret, args.verbose)
    
    if not success:
        logger.error("写入应用密钥失败")
        sys.exit(1)
    
    # 读取应用密钥进行验证
    app_secret = read_app_secret(args.etcd_endpoint, args.app_id, args.verbose)
    
    if app_secret is None:
        logger.error("验证应用密钥失败")
        sys.exit(1)
    
    if app_secret["app_id"] != args.app_id or app_secret["secret"] != args.secret:
        logger.error(f"应用密钥验证失败: 写入的密钥与读取的密钥不匹配")
        logger.error(f"写入的密钥: app_id={args.app_id}, secret={args.secret}")
        logger.error(f"读取的密钥: app_id={app_secret['app_id']}, secret={app_secret['secret']}")
        sys.exit(1)
    
    logger.info("应用密钥验证成功")
    
    # 如果需要测试密钥
    if args.test:
        test_app_secret(args.app_id, args.secret, args.verbose)
    
    sys.exit(0)

if __name__ == "__main__":
    main()
