#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
集成测试脚本: 测试 /app/node/:appid API
该脚本测试应用节点分配接口，验证认证逻辑和节点选择功能
"""

import json
import sys
import urllib.request
import urllib.error
import argparse
import logging
import time
import hashlib
from urllib.parse import urljoin, urlencode

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='测试 /app/node/:appid API')
    parser.add_argument('--host', default='http://localhost:8081', help='API服务器地址')
    parser.add_argument('--appid', default='test-app-123', help='测试应用ID')
    parser.add_argument('--secret', default='app-secret-key', help='应用密钥')
    parser.add_argument('--cip', default='*************', help='客户端IP地址')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细输出')
    return parser.parse_args()

def generate_token(app_secret, app_id, timestamp):
    """生成认证令牌"""
    # 按照服务端的逻辑，令牌生成方式为: md5(secret|appid|timestamp)
    content = f"{app_secret}|{app_id}|{timestamp}"
    md5_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
    return md5_hash

def test_get_node_for_app(base_url, app_id, app_secret, client_ip, verbose=False):
    """测试获取应用节点API"""
    # 生成当前时间戳（未来10分钟，确保不会过期）
    timestamp = str(int(time.time()) + 600)
    
    # 生成认证令牌
    token = generate_token(app_secret, app_id, timestamp)
    
    # 构建URL和查询参数
    url = urljoin(base_url, f'/app/node/{app_id}')
    query_params = {
        'token': token,
        'ts': timestamp,
        'cip': client_ip
    }
    
    # 将查询参数添加到URL
    full_url = f"{url}?{urlencode(query_params)}"
    
    # 创建请求
    req = urllib.request.Request(
        full_url,
        method='GET',
        headers={'Content-Type': 'application/json'}
    )
    
    try:
        # 发送请求
        logger.info(f"发送请求到: {full_url}")
        
        with urllib.request.urlopen(req) as response:
            # 读取并解析响应
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
            
            # 解析JSON响应
            try:
                result = json.loads(data)
                
                # 验证响应格式
                if 'node_address' in result:
                    logger.info(f"成功获取节点地址: {result['node_address']}")
                    logger.info("测试通过: API返回了有效的节点地址")
                    return True
                else:
                    logger.error(f"响应格式错误，缺少 'node_address' 字段: {result}")
                    return False
                
            except json.JSONDecodeError:
                logger.error(f"无法解析JSON响应: {data}")
                return False
                
    except urllib.error.HTTPError as e:
        logger.error(f"HTTP错误: {e.code} - {e.reason}")
        try:
            error_data = e.read().decode('utf-8')
            logger.error(f"错误详情: {error_data}")
        except:
            pass
        return False
        
    except urllib.error.URLError as e:
        logger.error(f"URL错误: {e.reason}")
        return False
        
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        return False

def test_missing_token(base_url, app_id, client_ip, verbose=False):
    """测试缺少令牌的情况"""
    # 生成当前时间戳
    timestamp = str(int(time.time()) + 600)
    
    # 构建URL和查询参数（不包含token）
    url = urljoin(base_url, f'/app/node/{app_id}')
    query_params = {
        'ts': timestamp,
        'cip': client_ip
    }
    
    # 将查询参数添加到URL
    full_url = f"{url}?{urlencode(query_params)}"
    
    # 创建请求
    req = urllib.request.Request(
        full_url,
        method='GET',
        headers={'Content-Type': 'application/json'}
    )
    
    try:
        logger.info(f"测试缺少令牌: {full_url}")
        
        with urllib.request.urlopen(req) as response:
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
                
            # 这个请求应该失败，所以如果成功了反而是错误
            logger.error("测试失败: 缺少令牌的请求不应该成功")
            return False
            
    except urllib.error.HTTPError as e:
        if e.code == 400:  # 期望返回400 Bad Request
            logger.info("测试通过: 缺少令牌时正确返回400状态码")
            return True
        else:
            logger.error(f"测试失败: 期望状态码400，实际返回{e.code}")
            try:
                error_data = e.read().decode('utf-8')
                logger.error(f"错误详情: {error_data}")
            except:
                pass
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def test_missing_timestamp(base_url, app_id, app_secret, client_ip, verbose=False):
    """测试缺少时间戳的情况"""
    # 生成认证令牌（使用一个固定的时间戳）
    fixed_timestamp = "1620000000"
    token = generate_token(app_secret, app_id, fixed_timestamp)
    
    # 构建URL和查询参数（不包含ts）
    url = urljoin(base_url, f'/app/node/{app_id}')
    query_params = {
        'token': token,
        'cip': client_ip
    }
    
    # 将查询参数添加到URL
    full_url = f"{url}?{urlencode(query_params)}"
    
    # 创建请求
    req = urllib.request.Request(
        full_url,
        method='GET',
        headers={'Content-Type': 'application/json'}
    )
    
    try:
        logger.info(f"测试缺少时间戳: {full_url}")
        
        with urllib.request.urlopen(req) as response:
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
                
            # 这个请求应该失败，所以如果成功了反而是错误
            logger.error("测试失败: 缺少时间戳的请求不应该成功")
            return False
            
    except urllib.error.HTTPError as e:
        if e.code == 400:  # 期望返回400 Bad Request
            logger.info("测试通过: 缺少时间戳时正确返回400状态码")
            return True
        else:
            logger.error(f"测试失败: 期望状态码400，实际返回{e.code}")
            try:
                error_data = e.read().decode('utf-8')
                logger.error(f"错误详情: {error_data}")
            except:
                pass
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def test_missing_client_ip(base_url, app_id, app_secret, verbose=False):
    """测试缺少客户端IP的情况"""
    # 生成当前时间戳
    timestamp = str(int(time.time()) + 600)
    
    # 生成认证令牌
    token = generate_token(app_secret, app_id, timestamp)
    
    # 构建URL和查询参数（不包含cip）
    url = urljoin(base_url, f'/app/node/{app_id}')
    query_params = {
        'token': token,
        'ts': timestamp
    }
    
    # 将查询参数添加到URL
    full_url = f"{url}?{urlencode(query_params)}"
    
    # 创建请求
    req = urllib.request.Request(
        full_url,
        method='GET',
        headers={'Content-Type': 'application/json'}
    )
    
    try:
        logger.info(f"测试缺少客户端IP: {full_url}")
        
        with urllib.request.urlopen(req) as response:
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
                
            # 这个请求应该失败，所以如果成功了反而是错误
            logger.error("测试失败: 缺少客户端IP的请求不应该成功")
            return False
            
    except urllib.error.HTTPError as e:
        if e.code == 400:  # 期望返回400 Bad Request
            logger.info("测试通过: 缺少客户端IP时正确返回400状态码")
            return True
        else:
            logger.error(f"测试失败: 期望状态码400，实际返回{e.code}")
            try:
                error_data = e.read().decode('utf-8')
                logger.error(f"错误详情: {error_data}")
            except:
                pass
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def test_invalid_token(base_url, app_id, client_ip, verbose=False):
    """测试无效令牌的情况"""
    # 生成当前时间戳
    timestamp = str(int(time.time()) + 600)
    
    # 使用无效的令牌
    invalid_token = "invalid_token_12345"
    
    # 构建URL和查询参数
    url = urljoin(base_url, f'/app/node/{app_id}')
    query_params = {
        'token': invalid_token,
        'ts': timestamp,
        'cip': client_ip
    }
    
    # 将查询参数添加到URL
    full_url = f"{url}?{urlencode(query_params)}"
    
    # 创建请求
    req = urllib.request.Request(
        full_url,
        method='GET',
        headers={'Content-Type': 'application/json'}
    )
    
    try:
        logger.info(f"测试无效令牌: {full_url}")
        
        with urllib.request.urlopen(req) as response:
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
                
            # 这个请求应该失败，所以如果成功了反而是错误
            logger.error("测试失败: 无效令牌的请求不应该成功")
            return False
            
    except urllib.error.HTTPError as e:
        if e.code == 401:  # 期望返回401 Unauthorized
            logger.info("测试通过: 无效令牌时正确返回401状态码")
            return True
        else:
            logger.error(f"测试失败: 期望状态码401，实际返回{e.code}")
            try:
                error_data = e.read().decode('utf-8')
                logger.error(f"错误详情: {error_data}")
            except:
                pass
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def test_expired_timestamp(base_url, app_id, app_secret, client_ip, verbose=False):
    """测试过期时间戳的情况"""
    # 生成过期的时间戳（过去的时间）
    expired_timestamp = str(int(time.time()) - 3600)  # 一小时前
    
    # 生成认证令牌
    token = generate_token(app_secret, app_id, expired_timestamp)
    
    # 构建URL和查询参数
    url = urljoin(base_url, f'/app/node/{app_id}')
    query_params = {
        'token': token,
        'ts': expired_timestamp,
        'cip': client_ip
    }
    
    # 将查询参数添加到URL
    full_url = f"{url}?{urlencode(query_params)}"
    
    # 创建请求
    req = urllib.request.Request(
        full_url,
        method='GET',
        headers={'Content-Type': 'application/json'}
    )
    
    try:
        logger.info(f"测试过期时间戳: {full_url}")
        
        with urllib.request.urlopen(req) as response:
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
                
            # 这个请求应该失败，所以如果成功了反而是错误
            logger.error("测试失败: 过期时间戳的请求不应该成功")
            return False
            
    except urllib.error.HTTPError as e:
        if e.code == 401:  # 期望返回401 Unauthorized
            logger.info("测试通过: 过期时间戳时正确返回401状态码")
            return True
        else:
            logger.error(f"测试失败: 期望状态码401，实际返回{e.code}")
            try:
                error_data = e.read().decode('utf-8')
                logger.error(f"错误详情: {error_data}")
            except:
                pass
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    args = parse_args()
    
    # 记录测试开始
    logger.info("开始测试 /app/node/:appid API")
    logger.info(f"服务器地址: {args.host}")
    logger.info(f"测试应用ID: {args.appid}")
    logger.info(f"客户端IP: {args.cip}")
    
    # 运行测试用例
    test_results = []
    
    # 测试1: 正常请求
    logger.info("\n===== 测试1: 正常请求 =====")
    test_results.append(test_get_node_for_app(args.host, args.appid, args.secret, args.cip, args.verbose))
    
    # 测试2: 缺少令牌
    logger.info("\n===== 测试2: 缺少令牌 =====")
    test_results.append(test_missing_token(args.host, args.appid, args.cip, args.verbose))
    
    # 测试3: 缺少时间戳
    logger.info("\n===== 测试3: 缺少时间戳 =====")
    test_results.append(test_missing_timestamp(args.host, args.appid, args.secret, args.cip, args.verbose))
    
    # 测试4: 缺少客户端IP
    logger.info("\n===== 测试4: 缺少客户端IP =====")
    test_results.append(test_missing_client_ip(args.host, args.appid, args.secret, args.verbose))
    
    # 测试5: 无效令牌
    logger.info("\n===== 测试5: 无效令牌 =====")
    test_results.append(test_invalid_token(args.host, args.appid, args.cip, args.verbose))
    
    # 测试6: 过期时间戳
    logger.info("\n===== 测试6: 过期时间戳 =====")
    test_results.append(test_expired_timestamp(args.host, args.appid, args.secret, args.cip, args.verbose))
    
    # 汇总测试结果
    total_tests = len(test_results)
    passed_tests = test_results.count(True)
    
    logger.info("\n===== 测试结果汇总 =====")
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {total_tests - passed_tests}")
    
    # 根据测试结果设置退出码
    if all(test_results):
        logger.info("所有测试通过!")
        sys.exit(0)
    else:
        logger.error("存在失败的测试!")
        sys.exit(1)

if __name__ == "__main__":
    main()
