#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
import argparse
import random
import logging
import urllib.request
import urllib.error
import urllib.parse
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('metrics_api_test')

class MetricsAPITester:
    """测试 Upload Tuner 的 /metrics/:nodeID API 端点"""
    
    def __init__(self, base_url="http://localhost:80", node_id="node1"):
        """
        初始化测试器
        
        Args:
            base_url: API 服务器基础 URL
            node_id: 要更新指标的节点 ID
        """
        self.base_url = base_url
        self.node_id = node_id
        self.metrics_url = f"{base_url}/metrics/{node_id}"
        logger.info(f"初始化测试器，目标 URL: {self.metrics_url}")
    
    def generate_random_metrics(self):
        """生成随机的节点指标数据"""
        # 使用 RFC3339 格式的时间戳，这是 Go 的 time.Time 类型在 JSON 中的标准格式
        # 例如："2025-05-12T11:48:02.828Z" 或 "2025-05-12T11:48:02.828+08:00"
        current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        
        return {
            "cpu_usage": round(random.uniform(10, 90), 2),
            "memory_usage": round(random.uniform(20, 85), 2),
            "disk_usage": round(random.uniform(30, 75), 2),
            "disk_free": round(random.uniform(50, 500), 2),
            "concurrent_tasks": random.randint(0, 100),
            "load_1min": round(random.uniform(0.1, 5.0), 2),
            "updated_at": current_time
        }
    
    def test_update_metrics(self, metrics=None):
        """
        测试更新节点指标 API
        
        Args:
            metrics: 要提交的指标数据，如果为 None 则生成随机数据
            
        Returns:
            响应对象
        """
        if metrics is None:
            metrics = self.generate_random_metrics()
            
        logger.info(f"提交指标数据: {json.dumps(metrics, ensure_ascii=False)}")
        
        try:
            headers = {
                'Content-Type': 'application/json',
            }
            
            # 将指标数据转换为 JSON 字符串，并编码为字节
            data = json.dumps(metrics).encode('utf-8')
            
            # 创建请求对象
            req = urllib.request.Request(
                self.metrics_url,
                data=data,
                headers=headers,
                method='POST'
            )
            
            # 发送请求并获取响应
            with urllib.request.urlopen(req) as response:
                status_code = response.getcode()
                response_body = response.read().decode('utf-8')
                
                logger.info(f"响应状态码: {status_code}")
                logger.info(f"响应内容: {response_body}")
                
                # 创建一个类似 requests.Response 的对象，以保持接口一致性
                class Response:
                    def __init__(self, status_code, text):
                        self.status_code = status_code
                        self.text = text
                
                return Response(status_code, response_body)
                
        except urllib.error.HTTPError as e:
            logger.error(f"HTTP 错误: {e.code} - {e.reason}")
            return None
        except urllib.error.URLError as e:
            logger.error(f"URL 错误: {str(e.reason)}")
            return None
        except Exception as e:
            logger.error(f"请求失败: {str(e)}")
            return None
    
    def run_continuous_test(self, interval=60, count=10):
        """
        连续运行测试多次
        
        Args:
            interval: 测试间隔时间（秒）
            count: 测试次数
        """
        logger.info(f"开始连续测试，间隔 {interval} 秒，共 {count} 次")
        
        for i in range(count):
            logger.info(f"执行第 {i+1}/{count} 次测试")
            response = self.test_update_metrics()
            
            if response and response.status_code == 200:
                logger.info("测试成功")
            else:
                logger.error("测试失败")
            
            if i < count - 1:  # 不是最后一次测试
                logger.info(f"等待 {interval} 秒后进行下一次测试...")
                time.sleep(interval)
        
        logger.info("连续测试完成")

def main():
    parser = argparse.ArgumentParser(description='测试 Upload Tuner 的 /metrics/:nodeID API')
    parser.add_argument('--url', default='http://localhost:8082', help='API 服务器基础 URL')
    parser.add_argument('--node-id', default='node1', help='节点 ID')
    parser.add_argument('--interval', type=int, default=60, help='连续测试的间隔时间（秒）')
    parser.add_argument('--count', type=int, default=1, help='连续测试的次数')
    parser.add_argument('--continuous', action='store_true', help='是否进行连续测试')
    
    args = parser.parse_args()
    
    tester = MetricsAPITester(args.url, args.node_id)
    
    if args.continuous:
        tester.run_continuous_test(args.interval, args.count)
    else:
        tester.test_update_metrics()

if __name__ == "__main__":
    main()
