#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import urllib.request
import urllib.error
import urllib.parse
import logging
import argparse
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('admin_nodes_api_test')

class AdminNodesAPITester:
    """测试 Upload Tuner 的 /admin/nodes API 端点"""
    
    def __init__(self, base_url="http://localhost:8081"):
        """
        初始化测试器
        
        Args:
            base_url: API 服务器基础 URL
        """
        self.base_url = base_url
        self.admin_nodes_url = f"{base_url}/admin/nodes"
        logger.info(f"初始化测试器，目标 URL: {self.admin_nodes_url}")
    
    def test_get_all_nodes(self):
        """
        测试获取所有节点信息 API
        
        Returns:
            响应对象
        """
        logger.info(f"发送请求到: {self.admin_nodes_url}")
        
        try:
            # 创建请求对象
            req = urllib.request.Request(
                self.admin_nodes_url,
                method='GET'
            )
            
            # 发送请求并获取响应
            with urllib.request.urlopen(req) as response:
                status_code = response.getcode()
                response_body = response.read().decode('utf-8')
                
                logger.info(f"响应状态码: {status_code}")
                
                # 尝试解析 JSON 响应
                try:
                    nodes_data = json.loads(response_body)
                    logger.info(f"成功获取 {len(nodes_data)} 个节点信息")
                    
                    # 打印节点信息
                    for i, node in enumerate(nodes_data):
                        logger.info(f"节点 {i+1}:")
                        logger.info(f"  ID: {node.get('id', 'N/A')}")
                        logger.info(f"  IP: {node.get('ip', 'N/A')}")
                        logger.info(f"  端口: {node.get('port', 'N/A')}")
                        logger.info(f"  容量: {node.get('capacity', 'N/A')}")
                        logger.info("-" * 40)
                    
                except json.JSONDecodeError:
                    logger.error(f"响应不是有效的 JSON 格式: {response_body}")
                
                # 创建一个类似 requests.Response 的对象，以保持接口一致性
                class Response:
                    def __init__(self, status_code, text, data=None):
                        self.status_code = status_code
                        self.text = text
                        self.data = data
                
                return Response(status_code, response_body, nodes_data if 'nodes_data' in locals() else None)
                
        except urllib.error.HTTPError as e:
            logger.error(f"HTTP 错误: {e.code} - {e.reason}")
            return None
        except urllib.error.URLError as e:
            logger.error(f"URL 错误: {str(e.reason)}")
            return None
        except Exception as e:
            logger.error(f"请求失败: {str(e)}")
            return None
    
    def run_continuous_test(self, interval=60, count=10):
        """
        连续运行测试多次
        
        Args:
            interval: 测试间隔时间（秒）
            count: 测试次数
        """
        logger.info(f"开始连续测试，间隔 {interval} 秒，共 {count} 次")
        
        for i in range(count):
            logger.info(f"执行第 {i+1}/{count} 次测试")
            response = self.test_get_all_nodes()
            
            if response and response.status_code == 200:
                logger.info("测试成功")
            else:
                logger.error("测试失败")
            
            if i < count - 1:  # 不是最后一次测试
                logger.info(f"等待 {interval} 秒后进行下一次测试...")
                time.sleep(interval)
        
        logger.info("连续测试完成")
    
    def compare_with_etcd(self, etcd_output):
        """
        将 API 返回的节点信息与 etcd 中的节点信息进行比较
        
        Args:
            etcd_output: etcd 查询输出的文件路径或内容字符串
        
        Returns:
            比较结果（布尔值）和差异信息
        """
        # 获取 API 返回的节点信息
        api_response = self.test_get_all_nodes()
        if not api_response or api_response.status_code != 200:
            logger.error("无法获取 API 节点信息")
            return False, "API 请求失败"
        
        try:
            api_nodes = api_response.data
            api_nodes_dict = {node['id']: node for node in api_nodes}
            
            # 解析 etcd 输出
            etcd_nodes = {}
            try:
                # 尝试作为文件路径读取
                with open(etcd_output, 'r') as f:
                    etcd_content = f.read()
            except:
                # 如果不是文件路径，则直接使用输入的内容
                etcd_content = etcd_output
            
            # 解析 etcd 输出内容
            import re
            node_blocks = re.split(r'-{40,}', etcd_content)
            for block in node_blocks:
                if 'Key: /nodes/' in block:
                    # 提取节点 ID
                    node_id_match = re.search(r'Key: /nodes/([^\n]+)', block)
                    if node_id_match:
                        node_id = node_id_match.group(1)
                        
                        # 提取 JSON 数据
                        json_match = re.search(r'Value \(JSON\):\n(.*?)(?=TTL:|$)', block, re.DOTALL)
                        if json_match:
                            try:
                                node_data = json.loads(json_match.group(1).strip())
                                etcd_nodes[node_id] = node_data
                            except:
                                logger.warning(f"无法解析节点 {node_id} 的 JSON 数据")
            
            # 比较节点信息
            differences = []
            
            # 检查 API 中有但 etcd 中没有的节点
            for node_id in api_nodes_dict:
                if node_id not in etcd_nodes:
                    differences.append(f"API 中有节点 {node_id}，但 etcd 中没有")
            
            # 检查 etcd 中有但 API 中没有的节点
            for node_id in etcd_nodes:
                if node_id not in api_nodes_dict:
                    differences.append(f"etcd 中有节点 {node_id}，但 API 中没有")
            
            # 检查共有节点的数据是否一致
            for node_id in api_nodes_dict:
                if node_id in etcd_nodes:
                    api_node = api_nodes_dict[node_id]
                    etcd_node = etcd_nodes[node_id]
                    
                    # 比较各个字段
                    for field in ['ip', 'port', 'capacity']:
                        if field in api_node and field in etcd_node:
                            if api_node[field] != etcd_node[field]:
                                differences.append(f"节点 {node_id} 的 {field} 字段不一致: API={api_node[field]}, etcd={etcd_node[field]}")
            
            if differences:
                logger.warning("发现差异:")
                for diff in differences:
                    logger.warning(f"  - {diff}")
                return False, differences
            else:
                logger.info("API 返回的节点信息与 etcd 中的节点信息完全一致")
                return True, None
            
        except Exception as e:
            logger.error(f"比较过程中发生错误: {str(e)}")
            return False, str(e)

def main():
    parser = argparse.ArgumentParser(description='测试 Upload Tuner 的 /admin/nodes API')
    parser.add_argument('--url', default='http://localhost:8081', help='API 服务器基础 URL')
    parser.add_argument('--interval', type=int, default=60, help='连续测试的间隔时间（秒）')
    parser.add_argument('--count', type=int, default=1, help='连续测试的次数')
    parser.add_argument('--continuous', action='store_true', help='是否进行连续测试')
    parser.add_argument('--compare-etcd', help='与 etcd 输出进行比较（提供 etcd 输出文件路径或直接粘贴输出内容）')
    
    args = parser.parse_args()
    
    tester = AdminNodesAPITester(args.url)
    
    if args.compare_etcd:
        tester.compare_with_etcd(args.compare_etcd)
    elif args.continuous:
        tester.run_continuous_test(args.interval, args.count)
    else:
        tester.test_get_all_nodes()

if __name__ == "__main__":
    main()
