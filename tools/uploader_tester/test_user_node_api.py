#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
集成测试脚本: 测试 /user/node/:userid API
该脚本测试用户节点分配接口，验证认证逻辑和节点选择功能
"""

import json
import sys
import urllib.request
import urllib.error
import argparse
import logging
import time
import jwt
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='测试 /user/node/:userid API')
    # 默认使用Nginx代理的地址
    # 也可以直接连接到upload_tuner服务：http://127.0.0.1:8081
    parser.add_argument('--host', default='http://127.0.0.1:80', help='API服务器地址')
    parser.add_argument('--userid', default='123', help='测试用户ID')
    parser.add_argument('--secret', default='TU/wO3eJdqbTppQ21qU6QVvrBFMb7I6qTfsDwB6FINc=', help='JWT密钥')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细输出')
    return parser.parse_args()

def generate_jwt_token(user_id, secret):
    """生成JWT令牌"""
    # 创建JWT载荷
    payload = {
        'user_id': int(user_id),  # 确保user_id是整数
        'exp': int(time.time()) + 3600,  # 令牌1小时后过期
        'iat': int(time.time())  # 令牌创建时间
    }
    
    # 使用密钥签名JWT
    token = jwt.encode(payload, secret, algorithm='HS256')
    
    # 如果token是bytes类型，转换为字符串
    if isinstance(token, bytes):
        token = token.decode('utf-8')
        
    return f'Bearer {token}'

def test_get_node(base_url, userid, secret, verbose=False):
    """测试获取节点API"""
    url = urljoin(base_url, f'/user/node/{userid}')
    
    # 生成JWT令牌
    token = generate_jwt_token(userid, secret)
    
    # 创建请求
    req = urllib.request.Request(
        url,
        method='GET',
        headers={
            'Authorization': token,
            'Content-Type': 'application/json',
            'Host': 'upload-tuner-api.local'  # 添加Host头以匹配Nginx配置
        }
    )
    
    try:
        # 发送请求
        logger.info(f"发送请求到: {url}")
        logger.info(f"请求头: Authorization={token}")
        
        with urllib.request.urlopen(req) as response:
            # 读取并解析响应
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
            
            # 解析JSON响应
            try:
                result = json.loads(data)
                
                # 验证响应格式
                if 'node_address' in result:
                    logger.info(f"成功获取节点地址: {result['node_address']}")
                    logger.info("测试通过: API返回了有效的节点地址")
                    return True
                else:
                    logger.error(f"响应格式错误，缺少 'node_address' 字段: {result}")
                    return False
                
            except json.JSONDecodeError:
                logger.error(f"无法解析JSON响应: {data}")
                return False
                
    except urllib.error.HTTPError as e:
        logger.error(f"HTTP错误: {e.code} - {e.reason}")
        try:
            error_data = e.read().decode('utf-8')
            logger.error(f"错误详情: {error_data}")
        except:
            pass
        return False
        
    except urllib.error.URLError as e:
        logger.error(f"URL错误: {e.reason}")
        return False
        
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        return False

def test_missing_auth_header(base_url, userid, verbose=False):
    """测试缺少认证头的情况"""
    url = urljoin(base_url, f'/user/node/{userid}')
    
    # 创建请求（不包含Authorization头）
    req = urllib.request.Request(
        url,
        method='GET',
        headers={
            'Content-Type': 'application/json',
            'Host': 'upload-tuner-api.local'  # 添加Host头以匹配Nginx配置
        }
    )
    
    try:
        logger.info(f"测试缺少认证头: {url}")
        
        with urllib.request.urlopen(req) as response:
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
                
            # 这个请求应该失败，所以如果成功了反而是错误
            logger.error("测试失败: 缺少认证头的请求不应该成功")
            return False
            
    except urllib.error.HTTPError as e:
        if e.code == 401:  # 期望返回401 Unauthorized
            logger.info("测试通过: 缺少认证头时正确返回401状态码")
            return True
        else:
            logger.error(f"测试失败: 期望状态码401，实际返回{e.code}")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def test_invalid_userid(base_url, secret, verbose=False):
    """测试无效的用户ID"""
    # 使用空用户ID
    url = urljoin(base_url, '/user/node/')
    
    # 生成JWT令牌，但使用不同的用户ID
    token = generate_jwt_token("999", secret)  # 使用一个不匹配的用户ID
    
    req = urllib.request.Request(
        url,
        method='GET',
        headers={
            'Authorization': token,
            'Content-Type': 'application/json'
        }
    )
    
    try:
        logger.info(f"测试无效用户ID: {url}")
        
        with urllib.request.urlopen(req) as response:
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
                
            # 这个请求应该失败，所以如果成功了反而是错误
            logger.error("测试失败: 无效用户ID的请求不应该成功")
            return False
            
    except urllib.error.HTTPError as e:
        # 期望返回404 Not Found（路由不匹配）或400 Bad Request
        if e.code in [404, 400]:
            logger.info(f"测试通过: 无效用户ID时正确返回{e.code}状态码")
            return True
        else:
            logger.error(f"测试失败: 期望状态码404或400，实际返回{e.code}")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def test_invalid_token(base_url, userid, verbose=False):
    """测试无效的令牌"""
    url = urljoin(base_url, f'/user/node/{userid}')
    
    # 使用无效的令牌
    invalid_token = "Bearer invalid.token.here"
    
    req = urllib.request.Request(
        url,
        method='GET',
        headers={
            'Authorization': invalid_token,
            'Content-Type': 'application/json'
        }
    )
    
    try:
        logger.info(f"测试无效令牌: {url}")
        
        with urllib.request.urlopen(req) as response:
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
                
            # 这个请求应该失败，所以如果成功了反而是错误
            logger.error("测试失败: 无效令牌的请求不应该成功")
            return False
            
    except urllib.error.HTTPError as e:
        if e.code == 401:  # 期望返回401 Unauthorized
            logger.info("测试通过: 无效令牌时正确返回401状态码")
            return True
        else:
            logger.error(f"测试失败: 期望状态码401，实际返回{e.code}")
            try:
                error_data = e.read().decode('utf-8')
                logger.error(f"错误详情: {error_data}")
            except:
                pass
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def test_mismatched_userid(base_url, userid, secret, verbose=False):
    """测试用户ID不匹配的情况"""
    url = urljoin(base_url, f'/user/node/{userid}')
    
    # 生成JWT令牌，但使用不同的用户ID
    different_userid = str(int(userid) + 1)  # 使用不同的用户ID
    token = generate_jwt_token(different_userid, secret)
    
    req = urllib.request.Request(
        url,
        method='GET',
        headers={
            'Authorization': token,
            'Content-Type': 'application/json'
        }
    )
    
    try:
        logger.info(f"测试用户ID不匹配: {url}")
        logger.info(f"路径中的用户ID: {userid}, 令牌中的用户ID: {different_userid}")
        
        with urllib.request.urlopen(req) as response:
            data = response.read().decode('utf-8')
            status_code = response.getcode()
            
            if verbose:
                logger.info(f"状态码: {status_code}")
                logger.info(f"响应数据: {data}")
                
            # 这个请求应该失败，所以如果成功了反而是错误
            logger.error("测试失败: 用户ID不匹配的请求不应该成功")
            return False
            
    except urllib.error.HTTPError as e:
        if e.code == 401:  # 期望返回401 Unauthorized
            logger.info("测试通过: 用户ID不匹配时正确返回401状态码")
            return True
        else:
            logger.error(f"测试失败: 期望状态码401，实际返回{e.code}")
            try:
                error_data = e.read().decode('utf-8')
                logger.error(f"错误详情: {error_data}")
            except:
                pass
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    args = parse_args()
    
    # 记录测试开始
    logger.info("开始测试 /user/node/:userid API")
    logger.info(f"服务器地址: {args.host}")
    logger.info(f"测试用户ID: {args.userid}")
    
    # 运行测试用例
    test_results = []
    
    # 测试1: 正常请求
    logger.info("\n===== 测试1: 正常请求 =====")
    test_results.append(test_get_node(args.host, args.userid, args.secret, args.verbose))
    
    # 测试2: 缺少认证头
    logger.info("\n===== 测试2: 缺少认证头 =====")
    test_results.append(test_missing_auth_header(args.host, args.userid, args.verbose))
    
    # 测试3: 无效用户ID
    logger.info("\n===== 测试3: 无效用户ID =====")
    test_results.append(test_invalid_userid(args.host, args.secret, args.verbose))
    
    # 测试4: 无效令牌
    logger.info("\n===== 测试4: 无效令牌 =====")
    test_results.append(test_invalid_token(args.host, args.userid, args.verbose))
    
    # 测试5: 用户ID不匹配
    logger.info("\n===== 测试5: 用户ID不匹配 =====")
    test_results.append(test_mismatched_userid(args.host, args.userid, args.secret, args.verbose))
    
    # 汇总测试结果
    total_tests = len(test_results)
    passed_tests = test_results.count(True)
    
    logger.info("\n===== 测试结果汇总 =====")
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {total_tests - passed_tests}")
    
    # 根据测试结果设置退出码
    if all(test_results):
        logger.info("所有测试通过!")
        sys.exit(0)
    else:
        logger.error("存在失败的测试!")
        sys.exit(1)

if __name__ == "__main__":
    main()
