#!/bin/bash

#########################################################################
# ETCD 集群启动脚本
#########################################################################
#
# 描述:
#   此脚本用于在本地快速启动一个多节点的 ETCD 集群，主要用于开发和测试目的。
#   脚本会启动指定数量的 ETCD 节点和一个 gRPC Proxy，所有数据存储在临时目录。
#
# 用法:
#   ./start_etcd_cluster.sh [节点数量]
#
# 参数:
#   [节点数量] - 可选，指定要启动的 ETCD 节点数量，默认为 3
#
# 示例:
#   ./start_etcd_cluster.sh         # 启动默认的 3 节点集群
#   ./start_etcd_cluster.sh 5       # 启动包含 5 个节点的集群
#
# 输出:
#   - 所有节点的进程 ID (PID)
#   - 节点日志文件位置
#   - 检查集群健康状态的命令示例
#   - 停止集群的命令
#   - 如何向集群添加新节点的说明
#
# 配置:
#   - 数据目录: /tmp/etcd-data/
#   - 日志目录: /tmp/
#   - 默认客户端端口: 从 2379 开始，每个节点递增 2
#   - 默认对等端口: 从 2380 开始，每个节点递增 2
#
# 注意:
#   1. 此脚本默认不会在退出时自动清理，需要手动停止集群
#   2. 所有数据默认存储在临时目录，系统重启后将丢失
#   3. 需要已安装 etcd 和 etcdctl 命令
#
#########################################################################

set -e # 发生错误时立即退出

# --- 显示帮助信息的函数 --- #
show_help() {
  cat << EOF
#########################################################################
# ETCD 集群启动脚本
#########################################################################

描述:
  此脚本用于在本地快速启动一个多节点的 ETCD 集群，主要用于开发和测试目的。
  脚本会启动指定数量的 ETCD 节点，并允许指定数据存储目录。

用法:
  ./start_etcd_cluster.sh [选项] [节点数量]

选项:
  -h, --help            显示此帮助信息并退出
  -d, --data-dir DIR    指定数据存储目录，默认为 /tmp/etcd-data
  -l, --log-dir DIR     指定日志存储目录，默认为 /tmp

参数:
  [节点数量]   可选，指定要启动的 ETCD 节点数量，默认为 3

示例:
  ./start_etcd_cluster.sh                                # 启动默认的 3 节点集群
  ./start_etcd_cluster.sh 5                              # 启动包含 5 个节点的集群
  ./start_etcd_cluster.sh -d /data/etcd 4                # 指定数据目录并启动 4 节点集群
  ./start_etcd_cluster.sh -l /var/log/etcd               # 指定日志目录
  ./start_etcd_cluster.sh -d /data/etcd -l /var/log/etcd # 指定数据和日志目录
  ./start_etcd_cluster.sh -h                             # 显示帮助信息

配置:
  - 数据目录: /tmp/etcd-data/ (可通过 -d 选项指定)
  - 日志目录: /tmp/ (可通过 -l 选项指定)
  - 默认客户端端口: 从 2379 开始，每个节点递增 2
  - 默认对等端口: 从 2380 开始，每个节点递增 2

注意:
  1. 此脚本默认不会在退出时自动清理，需要手动停止集群
  2. 可以使用 ./stop_etcd_cluster.sh 脚本停止集群并清理数据
  3. 所有数据存储在临时目录，系统重启后将丢失
  4. 需要已安装 etcd 和 etcdctl 命令
EOF
  exit 0
}

# --- 处理命令行参数 --- #

# 默认配置
DEFAULT_NUM_NODES=3
DEFAULT_DATA_DIR="/tmp/etcd-data"
DEFAULT_LOG_DIR="/tmp"

# 初始化参数
NUM_NODES=${DEFAULT_NUM_NODES}
ETCD_DATA_BASE_DIR=${DEFAULT_DATA_DIR}
LOG_BASE_DIR=${DEFAULT_LOG_DIR}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      ;;
    -d|--data-dir)
      if [[ -z "$2" || "$2" == -* ]]; then
        echo "错误: --data-dir 选项需要一个参数"
        echo "请使用 './start_etcd_cluster.sh -h' 查看帮助信息"
        exit 1
      fi
      ETCD_DATA_BASE_DIR="$2"
      shift 2
      ;;
    -l|--log-dir)
      if [[ -z "$2" || "$2" == -* ]]; then
        echo "错误: --log-dir 选项需要一个参数"
        echo "请使用 './start_etcd_cluster.sh -h' 查看帮助信息"
        exit 1
      fi
      LOG_BASE_DIR="$2"
      shift 2
      ;;
    [0-9]*)
      if [[ "$1" =~ ^[1-9][0-9]*$ ]]; then
        NUM_NODES=$1
        shift
      else
        echo "错误: 节点数量必须是一个正整数. 您输入的是: '$1'"
        echo "请使用 './start_etcd_cluster.sh -h' 查看帮助信息"
        exit 1
      fi
      ;;
    *)
      echo "错误: 未知参数 '$1'"
      echo "请使用 './start_etcd_cluster.sh -h' 查看帮助信息"
      exit 1
      ;;
  esac
done

# --- 配置 --- #
INITIAL_CLUSTER_TOKEN="etcd-cluster-1"
INITIAL_CLUSTER_STATE="new"

BASE_CLIENT_PORT=2379
BASE_PEER_PORT=2380

NODE_PIDS="" # 用于存储所有启动的etcd节点的PID

# --- 清理函数 --- #
cleanup() {
  if [ -n "${NODE_PIDS}" ]; then
    echo "正在停止所有etcd节点 (PIDs: ${NODE_PIDS})..."
    kill ${NODE_PIDS} >/dev/null 2>&1
    # 等待一小段时间确保进程有足够的时间退出
    sleep 1 
    # 再次检查进程是否仍在运行 (可选,更复杂的检查)
    # ps -p ${NODE_PIDS// /,} > /dev/null
    # if [ $? -eq 0 ]; then
    #   echo "警告: 部分进程可能未能成功停止: $(ps -p ${NODE_PIDS// /,}| awk 'NR>1 {print $1 "(" $4 ")'}')"
    # else
    #   echo "所有相关进程已成功发出停止信号。"
    # fi
    echo "已尝试停止所有相关进程。"
  else
    echo "没有找到需要停止的进程PIDs。"
  fi
  echo "正在清理数据目录: ${ETCD_DATA_BASE_DIR}/etcd-node* ..."
  rm -rf "${ETCD_DATA_BASE_DIR}/etcd-node*"
  echo "清理完成。"
}

# 设置trap，在脚本退出时执行清理
# trap cleanup EXIT # 暂时禁用自动清理，方便调试。可以手动调用 stop_etcd_cluster.sh

# --- 函数定义 --- #

# 启动单个etcd节点的函数
# 参数: $1:NODE_INDEX (1-based)
#       $2:CLUSTER_MEMBERS_STRING (完整的 initial-cluster 字符串)
start_etcd_node() {
  local node_index=$1
  local cluster_members_string=$2
  local node_name="etcd-node${node_index}"
  # 客户端端口偏移量: (node_index - 1) * 2, 因为每个节点需要1个客户端和1个对等端口
  # 为了避免端口冲突，对等端口也在客户端端口基础上增加
  # 0 -> 0, 1 -> 2, 2 -> 4
  local client_port_offset=$(( (node_index - 1) * 2 )) 
  local peer_port_offset=$(( (node_index - 1) * 2 )) 

  local listen_client_port=$((BASE_CLIENT_PORT + client_port_offset))
  local advertise_client_port=$((BASE_CLIENT_PORT + client_port_offset))
  local listen_peer_port=$((BASE_PEER_PORT + peer_port_offset))
  local advertise_peer_port=$((BASE_PEER_PORT + peer_port_offset))

  local data_dir="${ETCD_DATA_BASE_DIR}/${node_name}"
  local log_file="${LOG_BASE_DIR}/etcd_${node_name}.log"

  local listen_client_urls="http://0.0.0.0:${listen_client_port}"
  local advertise_client_urls="http://127.0.0.1:${advertise_client_port}"
  local listen_peer_urls="http://0.0.0.0:${listen_peer_port}"
  local initial_advertise_peer_urls="http://127.0.0.1:${advertise_peer_port}"

  echo "清理旧的 etcd 数据目录: ${data_dir}"
  rm -rf "${data_dir}"
  mkdir -p "${data_dir}"

  # 检查端口是否被占用
  if ss -lnt | grep -q ":${listen_client_port} " ; then
    echo "错误: 客户端端口 ${listen_client_port} 已被占用。请检查并释放该端口。" >&2
    exit 1
  fi
  if ss -lnt | grep -q ":${listen_peer_port} " ; then
    echo "错误: 对等端口 ${listen_peer_port} 已被占用。请检查并释放该端口。" >&2
    exit 1
  fi

  echo "正在启动 etcd 节点: ${node_name} (客户端 URL: ${advertise_client_urls}, 节点间 URL: ${initial_advertise_peer_urls})..."
  etcd \
    --name "${node_name}" \
    --data-dir "${data_dir}" \
    --listen-client-urls "${listen_client_urls}" \
    --advertise-client-urls "${advertise_client_urls}" \
    --listen-peer-urls "${listen_peer_urls}" \
    --initial-advertise-peer-urls "${initial_advertise_peer_urls}" \
    --initial-cluster "${cluster_members_string}" \
    --initial-cluster-token "${INITIAL_CLUSTER_TOKEN}" \
    --initial-cluster-state "${INITIAL_CLUSTER_STATE}" \
    --enable-v2 > "${log_file}" 2>&1 &

  ETCD_PID=$!
  NODE_PIDS="${NODE_PIDS} ${ETCD_PID}"
  echo "  -> 已启动 ${node_name}，PID: ${ETCD_PID}. 日志文件: ${log_file}"
}

# 检查数据目录并创建如果不存在
check_and_create_data_dir() {
  if [ ! -d "${ETCD_DATA_BASE_DIR}" ]; then
    echo "创建数据目录: ${ETCD_DATA_BASE_DIR}"
    mkdir -p "${ETCD_DATA_BASE_DIR}"
  fi
}

# 检查日志目录并创建如果不存在
check_and_create_log_dir() {
  if [ ! -d "${LOG_BASE_DIR}" ]; then
    echo "创建日志目录: ${LOG_BASE_DIR}"
    mkdir -p "${LOG_BASE_DIR}"
  fi
}

# 检查所有进程是否仍在运行
# 参数: $1:EXPECTED_PROCESS_COUNT
#       $2:PIDS_TO_CHECK (空格分隔的PID列表)
check_all_processes_running() {
  local EXPECTED_PROCESS_COUNT=$1
  local PIDS_TO_CHECK=$2
  local RUNNING_COUNT=0
  local MISSING_PIDS=""

  echo "检查已启动进程的状态..."
  # 去除首尾空格
  PIDS_TO_CHECK=$(echo "${PIDS_TO_CHECK}" | awk '{$1=$1;print}')

  for pid in ${PIDS_TO_CHECK};
  do
    if ps -p "${pid}" > /dev/null; then
      RUNNING_COUNT=$((RUNNING_COUNT + 1))
    else
      echo "警告: PID ${pid} 对应的进程似乎已退出或启动失败。"
      MISSING_PIDS="${MISSING_PIDS} ${pid}"
    fi
  done

  if [ "${RUNNING_COUNT}" -eq "${EXPECTED_PROCESS_COUNT}" ]; then
    echo "所有 ${EXPECTED_PROCESS_COUNT} 个 etcd 节点进程似乎都在运行。"
    return 0
  else
    echo "错误: 预期启动 ${EXPECTED_PROCESS_COUNT} 个进程，但只有 ${RUNNING_COUNT} 个似乎在运行。"
    echo "请检查 ${LOG_BASE_DIR}/ 目录下的各个节点日志文件以获取详细错误信息。"
    return 1
  fi
}

# --- 主逻辑 --- #

# 检查并创建数据和日志目录
check_and_create_data_dir
check_and_create_log_dir

echo "准备启动一个包含 ${NUM_NODES} 个节点的 etcd 集群..."
echo "数据存储目录: ${ETCD_DATA_BASE_DIR}"
echo "日志存储目录: ${LOG_BASE_DIR}"

# 构建 initial-cluster 字符串
PEER_URL_COMPONENTS=()
CLIENT_URL_COMPONENTS=()
INITIAL_CLIENT_ENDPOINTS_ARRAY=() # 用于健康检查

for i in $(seq 1 "${NUM_NODES}"); do
  node_name="etcd-node${i}"
  client_port_offset=$(( (i - 1) * 2 ))
  peer_port_offset=$(( (i - 1) * 2 ))
  
  advertise_client_port=$((BASE_CLIENT_PORT + client_port_offset))
  advertise_peer_port=$((BASE_PEER_PORT + peer_port_offset))

  current_peer_url="http://127.0.0.1:${advertise_peer_port}"
  current_client_url="http://127.0.0.1:${advertise_client_port}"

  PEER_URL_COMPONENTS+=("${node_name}=${current_peer_url}")
  CLIENT_URL_COMPONENTS+=("${current_client_url}")
done

INITIAL_CLUSTER_STRING=$(IFS=,; echo "${PEER_URL_COMPONENTS[*]}")
CLIENT_ENDPOINTS_STRING=$(IFS=,; echo "${CLIENT_URL_COMPONENTS[*]}")
INITIAL_CLIENT_ENDPOINTS_ARRAY=("${CLIENT_URL_COMPONENTS[@]}") # 用于健康检查

echo "集群初始化配置 (initial-cluster): ${INITIAL_CLUSTER_STRING}"

# 启动所有etcd节点
for i in $(seq 1 "${NUM_NODES}"); do
  start_etcd_node "${i}" "${INITIAL_CLUSTER_STRING}"
done

echo "等待集群初步稳定..."
sleep 5 # 给节点一些时间来选举leader等

# 等待至少一个etcd节点健康
MAX_WAIT_SECONDS=30
WAIT_INTERVAL=2
SECONDS_WAITED=0
ETCD_READY=false

FIRST_ETCD_CLIENT_ENDPOINT=${INITIAL_CLIENT_ENDPOINTS_ARRAY[0]}

if [ -z "${FIRST_ETCD_CLIENT_ENDPOINT}" ]; then
  echo "错误: 未能确定etcd客户端端点用于健康检查。"
  exit 1
fi

echo "正在检查 etcd 节点 (${FIRST_ETCD_CLIENT_ENDPOINT}) 健康状态，最多等待 ${MAX_WAIT_SECONDS} 秒..."

while [ ${SECONDS_WAITED} -lt ${MAX_WAIT_SECONDS} ]; do
  if ETCDCTL_API=3 etcdctl --endpoints="${FIRST_ETCD_CLIENT_ENDPOINT}" endpoint health --dial-timeout=1s > /dev/null 2>&1; then
    echo "Etcd 节点 (${FIRST_ETCD_CLIENT_ENDPOINT}) 已健康。"
    ETCD_READY=true
    break
  fi
  echo "等待 etcd 节点 (${FIRST_ETCD_CLIENT_ENDPOINT}) 就绪... (${SECONDS_WAITED}s / ${MAX_WAIT_SECONDS}s)"
  sleep ${WAIT_INTERVAL}
  SECONDS_WAITED=$((SECONDS_WAITED + WAIT_INTERVAL))
done

if [ "${ETCD_READY}" = false ]; then
  echo "警告: 等待 etcd 节点 (${FIRST_ETCD_CLIENT_ENDPOINT}) 健康超时 (${MAX_WAIT_SECONDS}s)。"
  echo "集群可能仍在初始化中，请检查日志文件以获取详细信息。"
  # 可以选择在此处退出，但我们继续执行以显示其他信息
  # exit 1
fi

# 检查已启动的etcd节点数量
ACTUAL_STARTED_NODES=0
if [ -n "${NODE_PIDS}" ]; then
  ACTUAL_STARTED_NODES=$(echo "${NODE_PIDS}" | wc -w)
fi

echo "----------------------------------------"
echo "etcd 集群启动流程已完成"
echo "已启动节点数量: ${ACTUAL_STARTED_NODES}"


echo ""
echo "所有节点 PIDs: ${NODE_PIDS}"
echo "节点日志位于 ${LOG_BASE_DIR}/etcd_etcd-node*.log"
echo "数据目录: ${ETCD_DATA_BASE_DIR}"
echo ""

CLIENT_ENDPOINTS_FOR_CTL="${CLIENT_ENDPOINTS_STRING}"
if [ -z "${CLIENT_ENDPOINTS_FOR_CTL}" ]; then
    CLIENT_ENDPOINTS_FOR_CTL="http://127.0.0.1:${BASE_CLIENT_PORT}" # 默认为单节点的第一个可能端口
fi

echo "要检查集群健康状况:"
echo "  ETCDCTL_API=3 etcdctl --endpoints=${CLIENT_ENDPOINTS_FOR_CTL} endpoint health"
echo "  ETCDCTL_API=3 etcdctl --endpoints=${CLIENT_ENDPOINTS_FOR_CTL} member list"

echo ""
echo "要停止所有启动的节点:"
echo "  kill ${NODE_PIDS}"
echo "  或者使用 './stop_etcd_cluster.sh' 脚本清理集群"

echo ""
echo "--- 如何向此集群添加新节点 ---"
echo "使用 'scripts/add_etcd_node.sh' 脚本可以方便地向当前集群添加新节点。"
echo ""
echo "用法示例:"
echo "  ./scripts/add_etcd_node.sh <新节点名称> <新节点IP> <新节点客户端端口> <新节点对等端口> <现有集群成员端点>"
echo ""
echo "参数说明:"
echo "  <新节点名称>:         新 etcd 节点的唯一名称 (例如: etcd-node-new)。"
echo "  <新节点IP>:           新节点的 IP 地址 (例如: 127.0.0.1 或者另一台机器的IP)。"
echo "  <新节点客户端端口>:   新节点监听客户端请求的端口 (例如: 2389)。确保此端口未被占用。"
echo "  <新节点对等端口>:     新节点用于集群成员间通信的对等端口 (例如: 2390)。确保此端口未被占用。"
echo "  <现有集群成员端点>: 现有集群中任一健康节点的客户端 URL (例如: ${FIRST_ETCD_CLIENT_ENDPOINT} 或 http://127.0.0.1:2379)。"
echo ""
echo "例如，要添加一个名为 'etcd-node4' 的新节点，IP为 '127.0.0.1'，客户端端口为 '2389'，对等端口为 '2390'，"
echo "并连接到现有成员 '${FIRST_ETCD_CLIENT_ENDPOINT}'，可以运行:"
echo "  ./add_etcd_node.sh etcd-node4 127.0.0.1 2389 2390 ${FIRST_ETCD_CLIENT_ENDPOINT}"
echo ""
echo "脚本会自动处理 'etcdctl member add' 以及新节点的启动配置。"
echo "请确保新节点规划使用的 IP 和端口对于新节点是可访问的，并且端口未被占用。"
echo "----------------------------------------"

# 检查已启动进程的状态...
if [ "${NUM_NODES}" -gt 0 ]; then
  # 等待一点时间确保所有进程有足够时间启动或失败
  sleep 2
  check_all_processes_running ${NUM_NODES} "${NODE_PIDS}"
  SCRIPT_EXIT_CODE=$?
else
  echo "未启动任何进程。"
  SCRIPT_EXIT_CODE=0
fi

echo "----------------------------------------"
exit ${SCRIPT_EXIT_CODE}
