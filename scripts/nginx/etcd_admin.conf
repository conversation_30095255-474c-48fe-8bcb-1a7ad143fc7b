upstream etcd_admin_backend {
    server 192.168.16.61:2379;
}

# ETCD管理系统服务器配置
server {
    listen 8080;
    server_name etcd-admin.local;
    
    # 访问日志和错误日志
    access_log /var/log/nginx/etcd_admin_access.log;
    error_log /var/log/nginx/etcd_admin_error.log;
    
    # 静态文件根目录
    root /home/<USER>/workspace/upload_tuner/web/etcd-admin/dist;
    index index.html;
    
    # 代理 etcd 的所有请求
    location /v2/ {
        proxy_pass http://etcd_admin_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,Api-Key';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        
        # 添加 CORS 头
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,Api-Key';
    }

    
    # 添加 /api/version 路径的代理
    location /api/version {
        proxy_pass http://etcd_admin_backend/version;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    
        # 添加 CORS 头
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    }

    # 处理前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 处理前端路由 - 节点管理页面
    location /nodes {
        try_files $uri $uri/ /index.html;
    }
    
    # 处理前端路由 - 应用密钥页面
    location /app-secrets {
        try_files $uri $uri/ /index.html;
    }
}
