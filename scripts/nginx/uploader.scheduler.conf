# Upload Tuner API 代理配置

# 定义上游服务
upstream upload_tuner_api {
    server *************:8081;
}

# API 代理服务器配置
server {
    listen 80 default_server;  # 使用默认80端口
    server_name api.upload-tuner;  # 指定多个服务器名称
    
    # 访问日志和错误日志
    access_log /var/log/nginx/upload_tuner_api_access.log;
    error_log /var/log/nginx/upload_tuner_api_error.log;
    
    # 基本状态检查端点
    location = /ping {
        proxy_pass http://upload_tuner_api/ping;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查端点
    location = /health {
        proxy_pass http://upload_tuner_api/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 版本信息端点
    location = /version {
        proxy_pass http://upload_tuner_api/version;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 用户获取节点接口
    location ~ ^/user/node/([^/]+)$ {
        proxy_pass http://upload_tuner_api/user/node/$1;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 应用获取节点接口
    location ~ ^/app/node/([^/]+)$ {
        proxy_pass http://upload_tuner_api/app/node/$1$is_args$args;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 管理员获取所有节点接口
    location = /admin/nodes {
        proxy_pass http://upload_tuner_api/admin/nodes;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 处理无效路径
    location = /user/node/ {
        return 404 '{"error": "Invalid user ID"}';
        add_header Content-Type application/json;
    }
    
    # 拒绝其他所有请求
    location / {
        return 403 '{"error": "Access forbidden"}';
        add_header Content-Type application/json;
    }
}
