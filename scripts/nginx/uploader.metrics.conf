# upload_tuner 指标代理配置，带 API 密钥验证

# 定义允许的 API 密钥
map $http_api_key $api_key_valid {
    default 0;
    # 这里可以添加多个有效的 API 密钥
    "_Tn2F-Kp*UHaVx9=" 1;
    "&2gpryq6f9*?Sx_SADV&" 1;
}

# 定义允许的 HTTP 方法和路径组合
map $request_method $is_put_method {
    default 0;
    PUT 1;
}

map $request_method $is_get_method {
    default 0;
    GET 1;
}

map $uri $is_nodes_path {
    default 0;
    ~^/v2/keys/nodes 1;
}

map $uri $is_health_path {
    default 0;
    /health 1;
}

# 组合以上条件判断是否允许操作
map "$is_put_method:$is_nodes_path:$is_get_method:$is_health_path" $is_allowed_operation {
    default 0;
    "1:1:0:0" 1;  # PUT + nodes路径
    "0:1:1:0" 1;  # GET + nodes路径
    "0:0:1:1" 1;  # GET + health路径
}

upstream etcd_backend {
    server 192.168.16.61:2379;
}

upstream upload_tuner_backend {
    server 192.168.5.218:8082;
}

# ETCD API 代理服务器配置
server {
    listen 80;
    server_name metrics.upload-tuner;

    # 访问日志和错误日志
    access_log /var/log/nginx/metrics_access.log;
    error_log /var/log/nginx/metrics_error.log;

    # 健康检查端点 - 不需要 API 密钥
    location = /health {
        return 200 'Nginx proxy for metrics is running';
        add_header Content-Type text/plain;
    }

    # 节点注册和发现 API
    location ~ ^/v2/keys/nodes(.*) {
        # 验证 API 密钥
        if ($api_key_valid = 0) {
            return 401 '{"error": "Invalid or missing API key"}';
            add_header Content-Type application/json;
        }
        
        # 验证是否是允许的操作
        if ($is_allowed_operation = 0) {
            return 403 '{"error": "Operation not permitted"}';
            add_header Content-Type application/json;
        }

        # 代理到 etcd v2 API
        proxy_pass http://etcd_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 指标上报接口代理
    location ~ ^/metrics/([^/]+)$ {
        # 验证 API 密钥
        if ($api_key_valid = 0) {
            return 401 '{"error": "Invalid or missing API key"}';
            add_header Content-Type application/json;
        }
        
        # 代理到 upload tuner 的指标上报接口
        proxy_pass http://upload_tuner_backend/metrics/$1;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 拒绝其他所有请求
    location / {
        return 403 '{"error": "Access forbidden"}';
        add_header Content-Type application/json;
    }
}
