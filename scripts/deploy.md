# Upload Tuner 系统部署文档

本文档详细说明了 Upload Tuner 系统的完整部署流程，包括 ETCD 集群、Upload Tuner 服务以及各个 Nginx 代理配置的部署步骤。

## 目录

1. [部署 ETCD 集群](#部署-etcd-集群)
2. [部署 Upload Tuner 服务](#部署-upload-tuner-服务)
3. [部署 Nginx 代理](#部署-nginx-代理)
   - [3.1 代理 Metrics API](#代理-metrics-api)
   - [3.2 代理 ETCD 管理系统](#代理-etcd-管理系统)
   - [3.3 代理节点分配 API](#代理节点分配-api)

## 部署 ETCD 集群

### 版本要求
* ETCD v3.3.11 或更高版本

### 部署步骤
1. 使用提供的脚本启动 ETCD 集群：
   ```bash
   ./scripts/start_etcd_cluster.sh [选项] [节点数量]
   ```

2. 脚本选项说明：
   - `-h, --help`            显示帮助信息并退出
   - `-d, --data-dir DIR`    指定数据存储目录，默认为 `/tmp/etcd-data`
   - `-l, --log-dir DIR`     指定日志存储目录，默认为 `/tmp`

3. 节点数量说明：
   - 默认启动 3 个节点的集群
   - 可以指定其他数量的节点，例如：
     ```bash
     ./scripts/start_etcd_cluster.sh 1  # 启动单节点集群
     ```

## 部署 Upload Tuner 服务

### 环境要求
* 已经部署好的 ETCD 集群

### 配置文件
在启动 Upload Tuner 服务前，需要先配置 `configs/config.yaml` 文件，主要配置项包括：

```yaml
# API服务器配置
api:
  host: 0.0.0.0  # 监听地址
  port: 8081     # 监听端口

# 指标服务器配置
metrics:
  host: 0.0.0.0  # 监听地址
  port: 8082     # 监听端口

# ETCD配置
etcd:
  endpoints: ["127.0.0.1:2379"]  # ETCD集群端点
  dial_timeout: 5                # 连接超时时间（秒）
  


```

### 运行

1. 运行服务：
   ```bash
   ./bin/upload_tuner
   ```

2. 测试服务是否正常运行：
   ```bash
   curl http://localhost:8081/ping
   curl http://localhost:8081/health
   curl http://localhost:8081/version
   ```

## 部署 Nginx 代理

### 代理 Metrics API

#### 配置说明
该配置用于代理 Upload Tuner 的指标上报 API，并提供 API 密钥验证功能。

#### 配置文件
配置文件路径：`scripts/nginx/uploader.metrics.conf`

主要配置项：
- 服务器名称：`metrics.upload-tuner.local`
- 监听端口：80
- 上游服务器：`127.0.0.1:8082`（Upload Tuner 的 Metrics 服务）

如果需要修改上游服务器地址，请修改配置文件中的 `upstream upload_tuner_backend` 部分。

#### 部署步骤

1. 复制配置文件到 Nginx 配置目录：
   ```bash
   sudo cp scripts/nginx/uploader.metrics.conf /etc/nginx/conf.d/
   ```

2. 检查配置是否正确：
   ```bash
   sudo nginx -t
   ```

3. 重启或重新加载 Nginx：
   ```bash
   sudo systemctl restart nginx
   # 或者
   sudo nginx -s reload
   ```

#### 测试部署成果

1. 测试健康检查接口：
   ```bash
   curl http://localhost/health
   # 或者指定服务器名称
   curl -H "Host: metrics.upload-tuner.local" http://localhost/health
   ```

2. 测试指标上报接口（需要 API 密钥）：
   ```bash
   curl -v -H "API-Key: _Tn2F-Kp*UHaVx9=" http://localhost/metrics/test-node
   ```

### 代理 ETCD 管理系统

#### 配置说明
该配置用于代理 ETCD 管理系统的前端和 API 请求，并提供 CORS 支持。

#### 配置文件
配置文件路径：`scripts/nginx/etcd_admin.conf`

主要配置项：
- 服务器名称：`etcd-admin.local`
- 监听端口：8080
- 静态文件路径：`/home/<USER>/workspace/upload_tuner/web/etcd-admin/dist`
- 上游服务器：`127.0.0.1:2379`（ETCD 服务）

如果需要修改上游服务器地址或静态文件路径，请修改配置文件中的相应部分。

#### 部署步骤

1. 复制配置文件到 Nginx 配置目录：
   ```bash
   sudo cp scripts/nginx/etcd_admin.conf /etc/nginx/conf.d/
   ```

2. 确保静态文件目录存在并包含前端文件：
   ```bash
   # 检查目录是否存在
   ls -la /home/<USER>/workspace/upload_tuner/web/etcd-admin/dist
   ```

3. 检查配置是否正确：
   ```bash
   sudo nginx -t
   ```

4. 重启或重新加载 Nginx：
   ```bash
   sudo systemctl restart nginx
   # 或者
   sudo nginx -s reload
   ```

#### 测试部署成果

1. 测试前端访问：
   ```bash
   curl http://localhost:8080/
   # 或者指定服务器名称
   curl -H "Host: etcd-admin.local" http://localhost:8080/
   ```

2. 测试 API 访问：
   ```bash
   curl http://localhost:8080/api/version
   ```

3. 在浏览器中访问管理界面：
   ```
   http://localhost:8080/
   ```

### 代理节点分配 API

#### 配置说明
该配置用于代理 Upload Tuner 的节点分配 API，包括用户节点、应用节点和管理接口。

#### 配置文件
配置文件路径：`scripts/nginx/uploader.scheduler.conf`

主要配置项：
- 服务器名称：`localhost api.upload-tuner.local`
- 监听端口：80
- 上游服务器：`127.0.0.1:8081`（Upload Tuner 的 API 服务）

如果需要修改上游服务器地址，请修改配置文件中的 `upstream upload_tuner_api_backend` 部分。

#### 部署步骤

1. 复制配置文件到 Nginx 配置目录：
   ```bash
   sudo cp scripts/nginx/uploader.scheduler.conf /etc/nginx/conf.d/
   ```

2. 检查配置是否正确：
   ```bash
   sudo nginx -t
   ```

3. 重启或重新加载 Nginx：
   ```bash
   sudo systemctl restart nginx
   # 或者
   sudo nginx -s reload
   ```

#### 测试部署成果

1. 测试基本接口：
   ```bash
   curl http://localhost/ping
   curl http://localhost/health
   curl http://localhost/version
   ```

2. 测试用户节点接口（需要 JWT 令牌）：
   ```bash
   # 使用测试脚本测试用户节点 API
   python3 tools/uploader_tester/test_user_node_api.py
   ```

## 系统测试

完成所有部署步骤后，可以运行集成测试来验证系统是否正常工作：

```bash
# 测试用户节点 API
python3 tools/uploader_tester/test_user_node_api.py

# 测试指标 API
python3 tools/uploader_tester/test_metrics_api.py
```

## 故障排除

### 常见问题

1. **Nginx 配置冲突**
   - 问题：多个配置文件监听相同的端口和服务器名称
   - 解决方法：使用不同的端口或服务器名称，或者禁用冲突的配置文件

2. **API 访问返回 404**
   - 问题：路由配置不正确或上游服务未运行
   - 解决方法：检查 Upload Tuner 服务是否运行，并验证 Nginx 配置中的代理路径

3. **认证失败**
   - 问题：请求返回 401 错误
   - 解决方法：检查 JWT 令牌或 API 密钥是否正确，并验证配置文件中的 `auth_secret` 设置
