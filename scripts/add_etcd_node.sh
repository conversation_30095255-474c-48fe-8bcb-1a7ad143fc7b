#!/bin/bash

set -e # Exit immediately if a command exits with a non-zero status.
set -u # Treat unset variables as an error when substituting.

# --- Script Configuration & Argument Parsing --- #
if [ "$#" -ne 5 ]; then
  echo "错误: 参数数量不正确。"
  echo "用法: $0 <新节点名称> <新节点IP> <新节点客户端端口> <新节点对等端口> <现有集群成员端点>"
  echo "示例: $0 etcd-node-new ************* 2389 2390 http://127.0.0.1:2379"
  exit 1
fi

NEW_NODE_NAME="$1"
NEW_NODE_IP="$2"
NEW_NODE_CLIENT_PORT="$3"
NEW_NODE_PEER_PORT="$4"
EXISTING_MEMBER_ENDPOINT="$5"

NEW_NODE_PEER_URL="http://${NEW_NODE_IP}:${NEW_NODE_PEER_PORT}"
NEW_NODE_CLIENT_URL="http://${NEW_NODE_IP}:${NEW_NODE_CLIENT_PORT}"

ETCD_DATA_BASE_DIR="/tmp/etcd-data"
LOG_BASE_DIR="/tmp"

# 确保数据和日志目录存在
mkdir -p "${ETCD_DATA_BASE_DIR}/${NEW_NODE_NAME}"

echo "--- 步骤 1: 向现有集群添加新成员 ---"
echo "正在使用端点 '${EXISTING_MEMBER_ENDPOINT}' 将 '${NEW_NODE_NAME}' (对等URL: ${NEW_NODE_PEER_URL}) 添加到集群..."

ADD_MEMBER_OUTPUT=$(ETCDCTL_API=3 etcdctl --endpoints="${EXISTING_MEMBER_ENDPOINT}" member add "${NEW_NODE_NAME}" --peer-urls="${NEW_NODE_PEER_URL}")

if [ $? -ne 0 ]; then
  echo "错误: 'etcdctl member add' 命令失败。"
  echo "输出:"
  echo "${ADD_MEMBER_OUTPUT}"
  exit 1
fi

echo "'etcdctl member add' 命令成功。输出:"
echo "${ADD_MEMBER_OUTPUT}"
echo ""

# 从输出中提取 ETCD_INITIAL_CLUSTER 和 ETCD_NAME
# 注意: ETCD_INITIAL_CLUSTER_STATE 将始终是 'existing' 对于通过 member add 添加的节点
# ETCD_NAME 应该与我们提供给 member add 的名称相同，但为了稳健性，可以从输出解析（如果格式固定）
# 更简单的方法是直接使用我们定义的 NEW_NODE_NAME 和 'existing' 状态

# 解析 ETCD_INITIAL_CLUSTER (这部分比较脆弱，依赖于 etcdctl 输出格式)
# 示例输出: ETCD_INITIAL_CLUSTER='etcd-node1=http://127.0.0.1:2380,etcd-node-new=http://*************:2390,etcd-node2=http://127.0.0.1:2382'
# 我们需要引号内的部分
ETCD_INITIAL_CLUSTER_LINE=$(echo "${ADD_MEMBER_OUTPUT}" | grep 'ETCD_INITIAL_CLUSTER=')

# 提取引号内的值 - 使用 cut 命令，以双引号为分隔符
PARSED_ETCD_INITIAL_CLUSTER=$(echo "${ETCD_INITIAL_CLUSTER_LINE}" | cut -d'"' -f2)

if [ -z "${PARSED_ETCD_INITIAL_CLUSTER}" ]; then
  echo "错误: 无法从 'etcdctl member add' 输出中解析 ETCD_INITIAL_CLUSTER。"
  echo "请检查上面的输出并手动设置。"
  exit 1
fi

ETCD_NAME_TO_USE="${NEW_NODE_NAME}" # member add 应该已使用此名称
ETCD_INITIAL_CLUSTER_TO_USE="${PARSED_ETCD_INITIAL_CLUSTER}"
ETCD_INITIAL_CLUSTER_STATE_TO_USE="existing"

echo "--- 步骤 2: 准备启动新节点 --- "
echo "将使用以下配置启动新节点 '${ETCD_NAME_TO_USE}':"
echo "  ETCD_NAME=${ETCD_NAME_TO_USE}"
echo "  ETCD_INITIAL_CLUSTER=${ETCD_INITIAL_CLUSTER_TO_USE}"
echo "  ETCD_INITIAL_CLUSTER_STATE=${ETCD_INITIAL_CLUSTER_STATE_TO_USE}"
echo "  数据目录: ${ETCD_DATA_BASE_DIR}/${ETCD_NAME_TO_USE}"
echo "  客户端URL: ${NEW_NODE_CLIENT_URL}"
echo "  对等URL: ${NEW_NODE_PEER_URL}"

echo ""
echo "正在启动新 etcd 节点 '${ETCD_NAME_TO_USE}'..."

# 检查端口是否被占用 (与 start_etcd_cluster.sh 中的逻辑类似)
if ss -lnt | grep -q ":${NEW_NODE_CLIENT_PORT} " ; then
    echo "错误: 客户端端口 ${NEW_NODE_CLIENT_PORT} 已被占用。请选择其他端口。" >&2
    exit 1
fi
if ss -lnt | grep -q ":${NEW_NODE_PEER_PORT} " ; then
    echo "错误: 对等端口 ${NEW_NODE_PEER_PORT} 已被占用。请选择其他端口。" >&2
    exit 1
fi 

# 清理并重新创建新的数据目录，以确保没有旧的冲突数据
echo "清理并重新创建新的数据目录: ${ETCD_DATA_BASE_DIR}/${ETCD_NAME_TO_USE}"
rm -rf "${ETCD_DATA_BASE_DIR}/${ETCD_NAME_TO_USE}"
mkdir -p "${ETCD_DATA_BASE_DIR}/${ETCD_NAME_TO_USE}"

# 启动 etcd 实例
# 注意: 在后台运行，并将 stdout/stderr 重定向到日志文件
etcd \
  --name="${ETCD_NAME_TO_USE}" \
  --initial-advertise-peer-urls="${NEW_NODE_PEER_URL}" \
  --listen-peer-urls="http://0.0.0.0:${NEW_NODE_PEER_PORT}" \
  --listen-client-urls="http://0.0.0.0:${NEW_NODE_CLIENT_PORT}" \
  --advertise-client-urls="${NEW_NODE_CLIENT_URL}" \
  --initial-cluster="${ETCD_INITIAL_CLUSTER_TO_USE}" \
  --initial-cluster-token="etcd-cluster-1" \
  --initial-cluster-state="${ETCD_INITIAL_CLUSTER_STATE_TO_USE}" \
  --data-dir="${ETCD_DATA_BASE_DIR}/${ETCD_NAME_TO_USE}" \
  --enable-v2 \
  > "${LOG_BASE_DIR}/etcd_${ETCD_NAME_TO_USE}.log" 2>&1 &

NODE_PID=$!
sleep 1 # 给点时间让进程启动或失败

if ps -p ${NODE_PID} > /dev/null; then
  echo "新 etcd 节点 '${ETCD_NAME_TO_USE}' 已启动，PID: ${NODE_PID}."
  echo "日志文件: ${LOG_BASE_DIR}/etcd_${ETCD_NAME_TO_USE}.log"
  echo "请稍等片刻，然后通过 'etcdctl member list --endpoints=${EXISTING_MEMBER_ENDPOINT}' 检查集群状态。"
else
  echo "错误: 新 etcd 节点 '${ETCD_NAME_TO_USE}' 启动失败。请检查日志: ${LOG_BASE_DIR}/etcd_${ETCD_NAME_TO_USE}.log" >&2
  exit 1
fi

echo "--- 完成 --- "
