#!/bin/sh

# 打包脚本：将前端必要文件和 upload tuner 服务可执行程序打包成 tar.gz 文件

# 设置变量
ROOT_DIR=$(cd "$(dirname "$0")/.." && pwd)
FRONTEND_DIR="$ROOT_DIR/web/etcd-admin"
CONFIG_DIR="$ROOT_DIR/configs"
SCRIPTS_DIR="$ROOT_DIR/scripts"
TEMP_DIR="/tmp/upload_tuner_package_temp"
OUTPUT_FILE="$ROOT_DIR/upload_tuner_package.tar.gz"

# 清理临时目录
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR/upload_tuner/etcd-admin"
mkdir -p "$TEMP_DIR/upload_tuner/bin"
mkdir -p "$TEMP_DIR/upload_tuner/configs"
mkdir -p "$TEMP_DIR/upload_tuner/scripts"
mkdir -p "$TEMP_DIR/upload_tuner/scripts/nginx"

echo "=== 开始打包 upload_tuner 项目 ==="
echo "项目根目录: $ROOT_DIR"

# 复制前端文件
echo "=== 复制前端文件 ==="
if [ -d "$FRONTEND_DIR/dist" ]; then
    cp -r "$FRONTEND_DIR/dist" "$TEMP_DIR/upload_tuner/etcd-admin/"
else
    echo "警告: 前端构建目录不存在，跳过复制"
fi

# 复制其他前端文件
for file in server.js start.sh package.json pnpm-lock.yaml .env.production; do
    if [ -f "$FRONTEND_DIR/$file" ]; then
        cp "$FRONTEND_DIR/$file" "$TEMP_DIR/upload_tuner/etcd-admin/"
        echo "已复制: $file"
    fi
done

# 复制可执行文件
echo "=== 复制 upload_tuner 可执行文件 ==="
if [ -f "$ROOT_DIR/upload_tuner" ]; then
    cp "$ROOT_DIR/upload_tuner" "$TEMP_DIR/upload_tuner/bin/"
    echo "已复制: upload_tuner 可执行程序"
else
    echo "错误: upload_tuner 可执行文件不存在"
    exit 1
fi

# 复制配置文件
echo "=== 复制配置文件 ==="
if [ -f "$CONFIG_DIR/config.yaml" ]; then
    cp "$CONFIG_DIR/config.yaml" "$TEMP_DIR/upload_tuner/configs/"
    echo "已复制: config.yaml 配置文件"
else
    echo "警告: config.yaml 配置文件不存在"
fi

# 复制 IP 范围文件
if [ -f "$CONFIG_DIR/merged_ip_range.txt" ]; then
    cp "$CONFIG_DIR/merged_ip_range.txt" "$TEMP_DIR/upload_tuner/configs/"
    echo "已复制: merged_ip_range.txt IP范围文件"
else
    echo "警告: merged_ip_range.txt IP范围文件不存在"
fi

if [ -f "$CONFIG_DIR/merged_ipv6_dat.txt" ]; then
    cp "$CONFIG_DIR/merged_ipv6_dat.txt" "$TEMP_DIR/upload_tuner/configs/"
    echo "已复制: merged_ipv6_dat.txt IPv6范围文件"
else
    echo "警告: merged_ipv6_dat.txt IPv6范围文件不存在"
fi

# 复制脚本文件
echo "=== 复制脚本文件 ==="
# 复制 start_etcd_cluster.sh
if [ -f "$SCRIPTS_DIR/start_etcd_cluster.sh" ]; then
    cp "$SCRIPTS_DIR/start_etcd_cluster.sh" "$TEMP_DIR/upload_tuner/scripts/"
    chmod +x "$TEMP_DIR/upload_tuner/scripts/start_etcd_cluster.sh"
    echo "已复制: start_etcd_cluster.sh"
else
    echo "警告: start_etcd_cluster.sh 文件不存在"
fi

# 复制 add_etcd_node.sh
if [ -f "$SCRIPTS_DIR/add_etcd_node.sh" ]; then
    cp "$SCRIPTS_DIR/add_etcd_node.sh" "$TEMP_DIR/upload_tuner/scripts/"
    chmod +x "$TEMP_DIR/upload_tuner/scripts/add_etcd_node.sh"
    echo "已复制: add_etcd_node.sh"
else
    echo "警告: add_etcd_node.sh 文件不存在"
fi

# 复制 nginx 配置文件
if [ -d "$SCRIPTS_DIR/nginx" ]; then
    cp "$SCRIPTS_DIR/nginx/"* "$TEMP_DIR/upload_tuner/scripts/nginx/"
    echo "已复制: nginx 目录下的所有配置文件"
else
    echo "警告: nginx 配置目录不存在"
fi

# 创建简单的说明文件
cat > "$TEMP_DIR/upload_tuner/README.md" << EOF
# Upload Tuner 部署包

此包含有：
1. etcd-admin 前端项目（位于 etcd-admin 目录）
2. upload_tuner 服务可执行程序（位于 bin 目录）
3. 配置文件（位于 configs 目录）：
   - config.yaml: 主配置文件
   - merged_ip_range.txt: IPv4范围文件
   - merged_ipv6_dat.txt: IPv6范围文件
4. 实用脚本（位于 scripts 目录）：
   - start_etcd_cluster.sh：启动 ETCD 集群
   - add_etcd_node.sh：添加 ETCD 节点
   - nginx/: Nginx 配置文件目录

## 部署说明

1. 配置 configs/config.yaml 文件
2. 配置 etcd-admin/.env.production 文件中的 ETCD 服务器地址
3. 启动 upload_tuner 服务
4. 启动前端服务

打包日期: $(date "+%Y-%m-%d %H:%M:%S")
EOF

# 创建压缩包
echo "=== 创建压缩包 ==="
cd /tmp
tar -czf "$OUTPUT_FILE" -C "$TEMP_DIR" .

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "=== 打包完成 ==="
echo "包已创建: $OUTPUT_FILE"
