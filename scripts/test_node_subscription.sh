#!/bin/bash

# 节点订阅功能测试脚本
# 此脚本用于测试 upload_tuner 服务的节点订阅功能

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 配置
UPLOAD_TUNER_DIR="/home/<USER>/workspace/upload_tuner"
ETCD_TESTER_DIR="${UPLOAD_TUNER_DIR}/tools/etcd_tester"
LOG_FILE="${UPLOAD_TUNER_DIR}/logs/app.log"
TEST_LOG_FILE="/tmp/node_subscription_test.log"

# 清理函数
cleanup() {
    echo -e "${YELLOW}清理测试环境...${NC}"
    # 停止所有测试进程
    if [ -n "$TESTER_PID" ]; then
        echo "停止 etcd_tester (PID: $TESTER_PID)"
        kill -TERM $TESTER_PID 2>/dev/null || true
    fi
    
    if [ -n "$TUNER_PID" ]; then
        echo "停止 upload_tuner (PID: $TUNER_PID)"
        kill -TERM $TUNER_PID 2>/dev/null || true
    fi
    
    echo -e "${GREEN}测试环境清理完成${NC}"
}

# 设置退出时的清理
trap cleanup EXIT

# 检查 upload_tuner 是否已构建
if [ ! -f "${UPLOAD_TUNER_DIR}/upload_tuner" ]; then
    echo -e "${YELLOW}构建 upload_tuner...${NC}"
    cd "${UPLOAD_TUNER_DIR}" && make
fi

# 构建 etcd_tester 工具
echo -e "${YELLOW}构建 etcd_tester 工具...${NC}"
cd "${ETCD_TESTER_DIR}" && go build -o etcd_tester

# 启动 upload_tuner 服务
echo -e "${YELLOW}启动 upload_tuner 服务...${NC}"
cd "${UPLOAD_TUNER_DIR}"
./upload_tuner > "${TEST_LOG_FILE}" 2>&1 &
TUNER_PID=$!

# 等待服务启动
echo -e "${YELLOW}等待服务启动 (5秒)...${NC}"
sleep 5

# 检查服务是否正常启动
if ! ps -p $TUNER_PID > /dev/null; then
    echo -e "${RED}upload_tuner 服务启动失败，请检查日志: ${TEST_LOG_FILE}${NC}"
    exit 1
fi

echo -e "${GREEN}upload_tuner 服务已启动 (PID: $TUNER_PID)${NC}"

# 清空日志文件，以便观察新的日志
truncate -s 0 "${LOG_FILE}"

# 运行 etcd_tester 工具进行测试
echo -e "${YELLOW}运行 etcd_tester 工具测试节点订阅功能...${NC}"
cd "${ETCD_TESTER_DIR}"
./etcd_tester --count 3 --delay 3 --node test-subscription > /tmp/etcd_tester.log 2>&1 &
TESTER_PID=$!

# 等待测试完成
echo -e "${YELLOW}测试进行中，等待 30 秒...${NC}"
sleep 30

# 检查日志中是否有节点变更的记录
echo -e "${YELLOW}检查 upload_tuner 日志中的节点变更记录...${NC}"
ADD_COUNT=$(grep -c "节点.*已添加或更新" "${LOG_FILE}" || true)
DELETE_COUNT=$(grep -c "节点.*已删除" "${LOG_FILE}" || true)
EVENT_COUNT=$(grep -c "处理节点变更事件" "${LOG_FILE}" || true)

echo "发现 $EVENT_COUNT 个节点变更事件记录"
echo "发现 $ADD_COUNT 个节点添加/更新记录"
echo "发现 $DELETE_COUNT 个节点删除记录"

# 判断测试结果
if [ $EVENT_COUNT -ge 3 ] && [ $ADD_COUNT -ge 3 ]; then
    echo -e "${GREEN}测试通过: 成功检测到节点变更事件${NC}"
    TEST_RESULT="通过"
else
    echo -e "${RED}测试失败: 未检测到足够的节点变更事件${NC}"
    echo -e "${YELLOW}请检查日志文件: ${LOG_FILE}${NC}"
    TEST_RESULT="失败"
fi

# 生成测试报告
cat > "${UPLOAD_TUNER_DIR}/node_subscription_test_report.txt" << EOF
# 节点订阅功能测试报告

测试时间: $(date)
测试结果: ${TEST_RESULT}

## 测试统计
- 节点变更事件总数: ${EVENT_COUNT}
- 节点添加/更新事件: ${ADD_COUNT}
- 节点删除事件: ${DELETE_COUNT}

## 测试详情
$(grep "处理节点变更事件\|节点.*已添加或更新\|节点.*已删除" "${LOG_FILE}" | tail -20)

EOF

echo -e "${YELLOW}测试报告已生成: ${UPLOAD_TUNER_DIR}/node_subscription_test_report.txt${NC}"

# 测试完成
if [ "$TEST_RESULT" = "通过" ]; then
    exit 0
else
    exit 1
fi
