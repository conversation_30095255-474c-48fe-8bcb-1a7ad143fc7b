#!/bin/sh

# 脚本用于停止由 start_etcd_cluster.sh 启动的所有 etcd 节点

# 使用与 start_etcd_cluster.sh 相同的集群令牌来识别进程
CLUSTER_TOKEN="etcd-cluster-dev"
PROCESS_PATTERN="etcd.*--initial-cluster-token ${CLUSTER_TOKEN}"
MAX_RETRIES=3
WAIT_TIME=5  # 增加等待时间到 5 秒

echo "正在查找与集群令牌 '${CLUSTER_TOKEN}' 相关的 etcd 进程..."

# 使用 pgrep -f 查找匹配完整命令行的进程 PID
# xargs 用于将可能的多行 PID 输出转换为空格分隔的单行
PIDS=$(pgrep -f "${PROCESS_PATTERN}" | xargs)

if [ -z "$PIDS" ]; then
  echo "未找到正在运行的、与令牌 '${CLUSTER_TOKEN}' 相关的 etcd 进程。"
  exit 0
else
  echo "找到以下 etcd 进程 PIDs: ${PIDS}"
  echo "正在发送 SIGTERM 信号以停止这些进程..."
  # 使用 kill 命令终止找到的所有进程
  kill ${PIDS}

  # 重试机制
  for retry in $(seq 1 $MAX_RETRIES); do
    echo "等待进程退出 (尝试 $retry/$MAX_RETRIES)..."
    sleep $WAIT_TIME

    # 检查进程是否仍在运行
    STILL_RUNNING=""
    for pid in $PIDS; do
      if ps -p $pid > /dev/null 2>&1; then
        STILL_RUNNING="$STILL_RUNNING $pid"
      fi
    done

    if [ -z "$STILL_RUNNING" ]; then
      echo "所有匹配的 etcd 进程已成功停止。"
      exit 0
    elif [ $retry -eq $MAX_RETRIES ]; then
      # 最后一次尝试，如果仍有进程运行，提示用户使用 SIGKILL
      echo "警告：以下进程在 $MAX_RETRIES 次尝试后仍未能正常停止:${STILL_RUNNING}"
      echo "你可能需要手动使用 'kill -9${STILL_RUNNING}' 来强制终止它们。"
      
      # 询问用户是否要自动强制终止
      echo -n "是否要自动强制终止这些进程? (y/n): "
      read answer
      if [ "$answer" = "y" ] || [ "$answer" = "Y" ]; then
        echo "正在强制终止进程${STILL_RUNNING}..."
        kill -9 $STILL_RUNNING
        echo "已发送 SIGKILL 信号。"
      fi
      
      exit 1
    else
      echo "以下进程仍在运行:${STILL_RUNNING}"
      echo "将在 $WAIT_TIME 秒后再次检查..."
    fi
  done
fi
