import ipaddress
import argparse

def parse_line(line):
    parts = line.strip().split(',')
    if len(parts) < 3:
        return None
    start_ip_str, end_ip_str = parts[0], parts[1]
    carrier = parts[-1]
    try:
        start_ip = ipaddress.ip_address(start_ip_str)
        end_ip = ipaddress.ip_address(end_ip_str)
        if start_ip > end_ip:
            print(f"Warning: Start IP {start_ip_str} is greater than End IP {end_ip_str} in line: {line.strip()}")
            return None
        return start_ip, end_ip, carrier
    except ValueError as e:
        print(f"Skipping invalid IP format in line: {line.strip()} - {e}")
        return None

def merge_ip_ranges_for_carrier(ranges_list):
    if not ranges_list:
        return []

    ranges_list.sort(key=lambda x: x[0])

    merged = []
    current_start, current_end, carrier = ranges_list[0][0], ranges_list[0][1], ranges_list[0][2]

    for i in range(1, len(ranges_list)):
        next_start, next_end, _ = ranges_list[i]
        if int(current_end) + 1 == int(next_start):
            current_end = next_end
        else:
            merged.append((current_start, current_end, carrier))
            current_start, current_end = next_start, next_end

    merged.append((current_start, current_end, carrier))
    return merged

def process_ip_file(input_file_path, output_file_path):
    ranges_by_carrier = {}
    # *** CHANGE HERE: Define allowed carriers ***
    allowed_carriers = {"联通", "电信", "移动"}
    try:
        with open(input_file_path, 'r', encoding='utf-8') as f_in:
            for line_num, line in enumerate(f_in, 1):
                if not line.strip():
                    continue
                parsed_data = parse_line(line)
                if parsed_data:
                    start_ip, end_ip, carrier = parsed_data
                    # *** CHANGE HERE: Filter by allowed carriers ***
                    if carrier in allowed_carriers:
                        if carrier not in ranges_by_carrier:
                            ranges_by_carrier[carrier] = []
                        ranges_by_carrier[carrier].append((start_ip, end_ip, carrier))
    except FileNotFoundError:
        print(f"Error: Input file '{input_file_path}' not found.")
        return
    except Exception as e:
        print(f"Error reading input file '{input_file_path}': {e}")
        return

    all_merged_ranges = []
    for carrier, ranges_list in ranges_by_carrier.items():
        merged_for_carrier = merge_ip_ranges_for_carrier(ranges_list)
        all_merged_ranges.extend(merged_for_carrier)

    all_merged_ranges.sort(key=lambda x: x[0])

    try:
        with open(output_file_path, 'w', encoding='utf-8') as f_out:
            for start_ip, end_ip, carrier in all_merged_ranges:
                f_out.write(f"{str(start_ip)} {str(end_ip)} {carrier}\n")
        print(f"Successfully processed IP ranges written to '{output_file_path}'")
    except Exception as e:
        print(f"Error writing to output file '{output_file_path}': {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Merge adjacent IP ranges for specific carriers.')
    parser.add_argument('input_file', help='Path to the input IP data file.')
    parser.add_argument('output_file', help='Path to the output file for processed IP ranges.')

    args = parser.parse_args()

    process_ip_file(args.input_file, args.output_file)
