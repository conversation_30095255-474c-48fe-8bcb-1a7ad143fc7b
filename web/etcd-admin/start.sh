#!/bin/bash

# 设置环境变量
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # 加载 nvm

# 处理命令行参数
PORT=3000
ETCD_URL="http://localhost:2379"
NGINX_PROXY=false

# 显示帮助信息
show_help() {
  echo "ETCD 管理系统构建脚本"
  echo "用法: $0 [选项]"
  echo ""
  echo "选项:"
  echo "  -h, --help                显示此帮助信息"
  echo "  -p, --port PORT           指定服务器端口 (默认: 3000)"
  echo "  -e, --etcd-url URL        指定 ETCD 服务器地址 (默认: 空，使用相对路径)"
  echo "  --build-only              仅构建前端，不启动服务器"
  echo "  --nginx-proxy             使用 Nginx 代理模式，将使用相对路径访问 etcd"
  echo ""
  echo "示例:"
  echo "  $0 --port 8080 --etcd-url http://etcd.example.com:2379"
  echo "  $0 --build-only --nginx-proxy"
  exit 0
}

# 解析命令行参数
BUILD_ONLY=false
while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      ;;
    -p|--port)
      PORT="$2"
      shift 2
      ;;
    -e|--etcd-url)
      ETCD_URL="$2"
      shift 2
      ;;
    --build-only)
      BUILD_ONLY=true
      shift
      ;;
    --nginx-proxy)
      NGINX_PROXY=true
      ETCD_URL=""
      shift
      ;;
    *)
      echo "错误: 未知参数 $1"
      echo "使用 -h 或 --help 查看帮助信息"
      exit 1
      ;;
  esac
done

# 显示配置信息
echo "ETCD 管理系统构建工具"
if [ "$NGINX_PROXY" = true ]; then
  echo "使用 Nginx 代理模式，将使用相对路径访问 etcd"
else
  echo "ETCD 服务器地址: $ETCD_URL"
fi

# 创建环境变量文件
echo "# 自动生成的环境变量文件" > .env.production.local
echo "VITE_ETCD_URL=$ETCD_URL" >> .env.production.local
echo "已配置 ETCD 服务器地址: $ETCD_URL"

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
  echo "正在安装依赖..."
  pnpm install
fi

# 构建前端
echo "正在构建前端应用..."
pnpm run build
echo "前端构建完成，静态文件位于 dist 目录"

# 如果只构建不启动服务器，则退出
if [ "$BUILD_ONLY" = true ]; then
  echo "构建完成，未启动服务器 (--build-only 模式)"
  echo "您可以使用 Nginx 等 Web 服务器部署 dist 目录中的静态文件"
  exit 0
fi

# 启动服务器
echo "启动服务器在端口 $PORT..."
PORT=$PORT node server.js
