import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'url'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())
  
  return {
    plugins: [vue()],
    define: {
      // 注入环境变量到客户端代码
      'process.env.VUE_APP_ETCD_URL': JSON.stringify(env.VITE_ETCD_URL || '')
    },
    server: {
      // 开发环境下代理 v2 路径到 etcd服务器
      proxy: {
        '/v2': {
          target: env.VITE_ETCD_URL || 'http://localhost:2379',
          changeOrigin: true
        }
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    build: {
      // 设置为较低的 ES 版本以兼容旧版本 Node.js
      target: 'es2019',
      minify: 'terser',
      terserOptions: {
        compress: {
          // 移除不兼容的语法特性
          ecma: 2019,
          // 禁用可能导致兼容性问题的特性
          drop_console: false,
          drop_debugger: true
        },
        format: {
          ecma: 2019
        }
      },
      rollupOptions: {
        output: {
          // 避免生成使用新语法的代码
          format: 'es',
          generatedCode: {
            constBindings: true,
            objectShorthand: true,
            // 禁用新的语法特性
            reservedNamesAsProps: false,
            symbols: false
          }
        }
      },
      outDir: 'dist',
      // 禁用使用 esbuild 作为压缩器，因为它可能生成不兼容的代码
      cssMinify: 'lightningcss'
    }
  }
})
