# ETCD 管理系统

这是一个用于管理 ETCD 中节点信息和 Token 信息的前端管理系统。该系统通过 ETCD 的 v2 HTTP API 接口，实现对节点和应用密钥的增删改查操作。

## 功能特点

- 节点管理：查看、添加、编辑和删除节点信息
- Token管理：查看、添加、编辑和删除应用密钥
- 直观的用户界面：基于 Vue 3 和 Element Plus 构建的现代化界面
- 实时数据更新：通过 ETCD 的 HTTP API 实时获取和更新数据

## 技术栈

- Vue 3：前端框架
- Vite：构建工具
- Element Plus：UI 组件库
- Axios：HTTP 客户端
- Express：后端服务器

## 项目结构

```
etcd-admin/
├── src/
│   ├── api/              # API 接口
│   ├── assets/           # 静态资源
│   ├── components/       # 组件
│   ├── router/           # 路由配置
│   ├── views/            # 页面视图
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── index.html            # HTML 模板
├── package.json          # 项目依赖
├── vite.config.js        # Vite 配置
├── server.js             # Express 服务器
├── start.sh              # 启动脚本
└── README.md             # 项目说明
```

## 安装和运行

### 前提条件

- Node.js 18+ 版本 (已通过 nvm 安装)
- 运行中的 ETCD 服务器

### 使用启动脚本

提供了一个方便的启动脚本，可以一键安装依赖、构建前端和启动服务器：

```bash
# 默认配置（端口3000，连接到localhost:2379的ETCD服务器）
./start.sh

# 自定义端口和ETCD服务器地址
./start.sh 8080 http://*************:2379
```

### 手动步骤

1. 安装依赖：

```bash
cd web/etcd-admin
npm install
```

2. 开发环境运行：

```bash
npm run dev
```

3. 构建生产环境：

```bash
npm run build
```

4. 启动服务器：

```bash
npm run start
# 或者指定ETCD服务器地址
ETCD_URL=http://your-etcd-server:2379 npm run start
```

## 配置

在 `vite.config.js` 文件中，可以配置开发环境下的 ETCD 服务器代理：

```javascript
export default defineConfig({
  plugins: [vue()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:2379', // 修改为你的 ETCD 服务器地址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

在生产环境中，可以通过环境变量配置 ETCD 服务器地址：

```bash
ETCD_URL=http://your-etcd-server:2379 node server.js
```

## 数据结构

### 节点信息

节点信息存储在 ETCD 的 `/nodes/` 目录下，每个节点的数据结构如下：

```json
{
  "id": "node1",
  "ip": "*************",
  "port": 8080,
  "capacity": 100
}
```

### 应用密钥

应用密钥存储在 ETCD 的 `/app_secrets/` 目录下，每个应用密钥的数据结构如下：

```json
{
  "appId": "app1",
  "secret": "your-secret-key"
}
```

## 注意事项

- 本系统直接操作 ETCD 数据，请谨慎使用，避免误操作导致数据丢失
- 建议在生产环境中添加适当的访问控制和权限验证
- 密钥信息敏感，请确保系统运行在安全的网络环境中
