<template>
  <div class="dashboard-container">
    <!-- 欢迎模块 -->
    <div class="welcome-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="welcome-card" shadow="hover">
            <div class="welcome-content">
              <div class="welcome-icon">
                <el-icon><el-icon-data-analysis /></el-icon>
              </div>
              <div class="welcome-text">
                <h2>欢迎使用 ETCD 管理系统</h2>
                <p>当前连接到 ETCD 服务器: <el-tag size="small" type="success">{{ etcdServer }}</el-tag></p>
                <p>服务器状态: 
                  <el-tag v-if="serverStatus === 'online'" size="small" type="success">在线</el-tag>
                  <el-tag v-else size="small" type="danger">离线</el-tag>
                </p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="data-card" shadow="hover">
            <div class="data-card-content">
              <div class="data-card-icon node-icon">
                <el-icon><el-icon-cpu /></el-icon>
              </div>
              <div class="data-card-info">
                <div class="data-card-title">节点总数</div>
                <div class="data-card-value" v-loading="loading.nodes">
                  {{ stats.nodeCount }}
                </div>
                <div class="data-card-footer">
                  <el-button type="primary" size="small" @click="$router.push('/nodes')">管理节点</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="data-card" shadow="hover">
            <div class="data-card-content">
              <div class="data-card-icon token-icon">
                <el-icon><el-icon-key /></el-icon>
              </div>
              <div class="data-card-info">
                <div class="data-card-title">Token 总数</div>
                <div class="data-card-value" v-loading="loading.appSecrets">
                  {{ stats.appSecretCount }}
                </div>
                <div class="data-card-footer">
                  <el-button type="primary" size="small" @click="$router.push('/app-secrets')">管理 Token</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="data-card" shadow="hover">
            <div class="data-card-content">
              <div class="data-card-icon version-icon">
                <el-icon><el-icon-info-filled /></el-icon>
              </div>
              <div class="data-card-info">
                <div class="data-card-title">ETCD 版本</div>
                <div class="data-card-value" v-loading="loading.version">
                  {{ etcdVersion.etcdserver || '未知' }}
                </div>
                <div class="data-card-footer">
                  <el-tag size="small" type="info">集群: {{ etcdVersion.etcdcluster || '未知' }}</el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="data-card" shadow="hover">
            <div class="data-card-content">
              <div class="data-card-icon time-icon">
                <el-icon><el-icon-timer /></el-icon>
              </div>
              <div class="data-card-info">
                <div class="data-card-title">系统状态</div>
                <div class="data-card-value">
                  <el-tag type="success">正常</el-tag>
                </div>
                <div class="data-card-footer">
                  上次更新: {{ formatTime(lastUpdateTime) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 功能导航区 -->
    <div class="features-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <h3>快速操作</h3>
              </div>
            </template>
            <div class="features-grid">
              <div class="feature-item" @click="$router.push('/nodes')">
                <el-icon><el-icon-cpu /></el-icon>
                <span>节点管理</span>
              </div>
              <div class="feature-item" @click="$router.push('/app-secrets')">
                <el-icon><el-icon-key /></el-icon>
                <span>Token 管理</span>
              </div>
              <div class="feature-item" @click="refreshData">
                <el-icon><el-icon-refresh /></el-icon>
                <span>刷新数据</span>
              </div>
              <div class="feature-item">
                <el-icon><el-icon-document /></el-icon>
                <span>查看文档</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 系统信息区 -->
    <div class="system-info-section">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <h3>系统信息</h3>
                <el-button type="primary" size="small" plain @click="refreshData">
                  <el-icon><el-icon-refresh /></el-icon> 刷新
                </el-button>
              </div>
            </template>
            <div class="system-info-content">
              <p><strong>ETCD 管理系统版本:</strong> 1.0.0</p>
              <p><strong>连接状态:</strong> 
                <el-tag v-if="serverStatus === 'online'" size="small" type="success">已连接</el-tag>
                <el-tag v-else size="small" type="danger">未连接</el-tag>
              </p>
              <p><strong>功能概要:</strong></p>
              <ul>
                <li><strong>节点管理</strong>: 查看、添加、编辑和删除节点信息</li>
                <li><strong>Token 管理</strong>: 管理应用密钥数据，包括创建、更新和删除操作</li>
                <li><strong>实时监控</strong>: 监控 ETCD 中的数据变化，及时响应系统状态</li>
              </ul>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue'
import { getAllNodes, getAllAppSecrets } from '../api/etcd'
import axios from 'axios'
import {
  DataAnalysis as ElIconDataAnalysis,
  Cpu as ElIconCpu,
  Key as ElIconKey,
  InfoFilled as ElIconInfoFilled,
  Timer as ElIconTimer,
  Refresh as ElIconRefresh,
  Document as ElIconDocument
} from '@element-plus/icons-vue'

// 从 App.vue 注入的刷新方法
const injectedRefreshData = inject('refreshData', () => {})

// 加载状态
const loading = ref({
  nodes: true,
  appSecrets: true,
  version: true
})

// 统计数据
const stats = ref({
  nodeCount: 0,
  appSecretCount: 0
})

// ETCD 服务器信息
const etcdServer = ref(window.location.hostname === 'localhost' ? 'http://localhost:2379' : window.location.origin)
const serverStatus = ref('offline')
const etcdVersion = ref({})
const lastUpdateTime = ref(new Date())

// 格式化时间
const formatTime = (time) => {
  if (!time) return '-'
  const date = new Date(time)
  return date.toLocaleString()
}

// 获取 ETCD 版本信息
const getEtcdVersion = async () => {
  loading.value.version = true
  try {
    const response = await axios.get('/api/version')
    etcdVersion.value = response.data || {}
    serverStatus.value = 'online'
  } catch (error) {
    console.error('获取 ETCD 版本信息失败:', error)
    serverStatus.value = 'offline'
    etcdVersion.value = {}
  } finally {
    loading.value.version = false
  }
}

// 加载所有数据
const loadAllData = async () => {
  loading.value.nodes = true
  loading.value.appSecrets = true
  
  try {
    // 并行加载数据
    const [nodesRes, appSecretsRes] = await Promise.allSettled([
      getAllNodes(),
      getAllAppSecrets()
    ])
    
    // 处理节点数据
    if (nodesRes.status === 'fulfilled' && nodesRes.value.data.node && nodesRes.value.data.node.nodes) {
      stats.value.nodeCount = nodesRes.value.data.node.nodes.length
      serverStatus.value = 'online'
    } else {
      stats.value.nodeCount = 0
      if (nodesRes.status === 'rejected') {
        serverStatus.value = 'offline'
      }
    }
    
    // 处理应用密钥数据
    if (appSecretsRes.status === 'fulfilled' && appSecretsRes.value.data.node && appSecretsRes.value.data.node.nodes) {
      stats.value.appSecretCount = appSecretsRes.value.data.node.nodes.length
      serverStatus.value = 'online'
    } else {
      stats.value.appSecretCount = 0
      if (appSecretsRes.status === 'rejected' && serverStatus.value !== 'offline') {
        serverStatus.value = 'offline'
      }
    }
    
    // 更新时间
    lastUpdateTime.value = new Date()
  } catch (error) {
    console.error('加载数据失败:', error)
    serverStatus.value = 'offline'
  } finally {
    loading.value.nodes = false
    loading.value.appSecrets = false
  }
}

// 刷新数据
const refreshData = () => {
  loadAllData()
  getEtcdVersion()
}

// 组件挂载时加载数据
onMounted(() => {
  loadAllData()
  getEtcdVersion()
})
</script>

<style scoped>
/* 仪表盘容器 */
.dashboard-container {
  padding: 20px;
}

/* 各个区域间距 */
.welcome-section,
.stats-section,
.features-section,
.system-info-section {
  margin-bottom: 24px;
}

/* 欢迎卡片样式 */
.welcome-card {
  background: linear-gradient(135deg, #1890ff, #0050b3);
  color: white;
}

.welcome-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.welcome-icon {
  font-size: 48px;
  margin-right: 20px;
}

.welcome-text h2 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 24px;
}

.welcome-text p {
  margin: 5px 0;
  font-size: 14px;
}

/* 数据卡片样式 */
.data-card {
  height: 100%;
  transition: all 0.3s;
}

.data-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.data-card-content {
  display: flex;
  padding: 10px;
}

.data-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  margin-right: 15px;
}

.node-icon {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.token-icon {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.version-icon {
  background-color: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.time-icon {
  background-color: rgba(245, 34, 45, 0.1);
  color: #f5222d;
}

.data-card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.data-card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.data-card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  min-height: 30px;
  display: flex;
  align-items: center;
}

.data-card-footer {
  margin-top: auto;
  font-size: 12px;
  color: #999;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 功能导航区样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 20px;
  padding: 10px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.3s;
}

.feature-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-3px);
}

.feature-item .el-icon {
  font-size: 24px;
  margin-bottom: 10px;
  color: #1890ff;
}

.feature-item span {
  font-size: 14px;
  color: #333;
}

/* 系统信息区样式 */
.system-info-content {
  line-height: 1.8;
}

.system-info-content p {
  margin: 5px 0;
}

.system-info-content ul {
  padding-left: 20px;
  margin: 5px 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .data-card-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .data-card-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
