<template>
  <div class="nodes-container">
    <div class="page-header">
      <h2>节点管理</h2>
      <el-button type="primary" @click="showAddNodeDialog">添加节点</el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="nodes"
      border
      style="width: 100%">
      <el-table-column prop="id" label="节点ID" width="180" />
      <el-table-column prop="ip" label="IP地址" width="180" />
      <el-table-column prop="port" label="端口" width="100" />
      <el-table-column prop="capacity" label="最大承载能力" width="150" />
      <el-table-column prop="ttl" label="TTL(秒)" width="100" />
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="editNode(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="confirmDeleteNode(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑节点对话框 -->
    <el-dialog
      v-model="nodeDialog.visible"
      :title="nodeDialog.isEdit ? '编辑节点' : '添加节点'"
      width="500px">
      <el-form :model="nodeForm" label-width="120px" :rules="rules" ref="nodeFormRef">
        <el-form-item label="节点ID" prop="id" :disabled="nodeDialog.isEdit">
          <el-input v-model="nodeForm.id" :disabled="nodeDialog.isEdit" />
        </el-form-item>
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="nodeForm.ip" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="nodeForm.port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="最大承载能力" prop="capacity">
          <el-input-number v-model="nodeForm.capacity" :min="1" />
        </el-form-item>
        <el-form-item label="TTL(秒)" prop="ttl">
          <el-input-number v-model="nodeForm.ttl" :min="0" :step="10" />
          <div class="form-tip">设置为0表示永不过期</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="nodeDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveNode" :loading="saving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAllNodes, setNode, deleteNode } from '../api/etcd'

const loading = ref(true)
const saving = ref(false)
const nodes = ref([])
const nodeFormRef = ref(null)

// 节点表单
const nodeForm = reactive({
  id: '',
  ip: '',
  port: 8080,
  capacity: 100,
  ttl: 60
})

// 表单验证规则
const rules = {
  id: [
    { required: true, message: '请输入节点ID', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '节点ID只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号范围为1-65535', trigger: 'blur' }
  ],
  capacity: [
    { required: true, message: '请输入最大承载能力', trigger: 'blur' },
    { type: 'number', min: 1, message: '最大承载能力必须大于0', trigger: 'blur' }
  ]
}

// 对话框状态
const nodeDialog = reactive({
  visible: false,
  isEdit: false,
  currentNodeId: ''
})

// 加载所有节点数据
const loadNodes = async () => {
  loading.value = true
  try {
    const response = await getAllNodes()
    if (response.data && response.data.node && response.data.node.nodes) {
      nodes.value = response.data.node.nodes.map(node => {
        const nodeData = JSON.parse(node.value)
        return {
          ...nodeData,
          id: node.key.split('/').pop(),
          ttl: node.ttl || 0
        }
      })
    } else {
      nodes.value = []
    }
  } catch (error) {
    console.error('加载节点数据失败:', error)
    ElMessage.error('加载节点数据失败')
  } finally {
    loading.value = false
  }
}

// 显示添加节点对话框
const showAddNodeDialog = () => {
  nodeDialog.isEdit = false
  nodeDialog.currentNodeId = ''
  Object.assign(nodeForm, {
    id: '',
    ip: '',
    port: 8080,
    capacity: 100,
    ttl: 60
  })
  nodeDialog.visible = true
}

// 显示编辑节点对话框
const editNode = (row) => {
  nodeDialog.isEdit = true
  nodeDialog.currentNodeId = row.id
  Object.assign(nodeForm, {
    id: row.id,
    ip: row.ip,
    port: row.port,
    capacity: row.capacity,
    ttl: row.ttl || 60
  })
  nodeDialog.visible = true
}

// 保存节点
const saveNode = async () => {
  if (!nodeFormRef.value) return
  
  await nodeFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    saving.value = true
    try {
      const nodeId = nodeDialog.isEdit ? nodeDialog.currentNodeId : nodeForm.id
      const nodeData = {
        id: nodeId,
        ip: nodeForm.ip,
        port: nodeForm.port,
        capacity: nodeForm.capacity
      }
      
      await setNode(nodeId, nodeData, nodeForm.ttl)
      ElMessage.success(nodeDialog.isEdit ? '节点更新成功' : '节点添加成功')
      nodeDialog.visible = false
      loadNodes()
    } catch (error) {
      console.error('保存节点失败:', error)
      ElMessage.error('保存节点失败')
    } finally {
      saving.value = false
    }
  })
}

// 确认删除节点
const confirmDeleteNode = (row) => {
  ElMessageBox.confirm(
    `确定要删除节点 ${row.id} 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteNode(row.id)
      ElMessage.success('节点删除成功')
      loadNodes()
    } catch (error) {
      console.error('删除节点失败:', error)
      ElMessage.error('删除节点失败')
    }
  }).catch(() => {
    // 用户取消删除操作
  })
}

onMounted(() => {
  loadNodes()
})
</script>

<style scoped>
.nodes-container {
  padding: 20px 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
