<template>
  <div class="app-secrets-container">
    <div class="page-header">
      <h2>Token管理</h2>
      <el-button type="primary" @click="showAddSecretDialog">添加Token</el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="appSecrets"
      border
      style="width: 100%">
      <el-table-column prop="appId" label="应用ID" width="180" />
      <el-table-column label="密钥" min-width="300">
        <template #default="scope">
          <div class="secret-display">
            <span v-if="!scope.row.showSecret">{{ maskSecret(scope.row.secret) }}</span>
            <span v-else>{{ scope.row.secret }}</span>
            <el-button link type="primary" @click="toggleSecretVisibility(scope.row)">
              {{ scope.row.showSecret ? '隐藏' : '显示' }}
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="editSecret(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="confirmDeleteSecret(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑密钥对话框 -->
    <el-dialog
      v-model="secretDialog.visible"
      :title="secretDialog.isEdit ? '编辑Token' : '添加Token'"
      width="500px">
      <el-form :model="secretForm" label-width="120px" :rules="rules" ref="secretFormRef">
        <el-form-item label="应用ID" prop="appId" :disabled="secretDialog.isEdit">
          <el-input v-model="secretForm.appId" :disabled="secretDialog.isEdit" />
        </el-form-item>
        <el-form-item label="密钥" prop="secret">
          <el-input v-model="secretForm.secret" :type="showSecretInput ? 'text' : 'password'" />
          <div class="input-actions">
            <el-button link type="primary" @click="showSecretInput = !showSecretInput">
              {{ showSecretInput ? '隐藏' : '显示' }}
            </el-button>
            <el-button link type="primary" @click="generateSecret">生成随机密钥</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="secretDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveSecret" :loading="saving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAllAppSecrets, setAppSecret, deleteAppSecret } from '../api/etcd'

const loading = ref(true)
const saving = ref(false)
const appSecrets = ref([])
const secretFormRef = ref(null)
const showSecretInput = ref(false)

// 密钥表单
const secretForm = reactive({
  appId: '',
  secret: ''
})

// 表单验证规则
const rules = {
  appId: [
    { required: true, message: '请输入应用ID', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '应用ID只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  secret: [
    { required: true, message: '请输入密钥', trigger: 'blur' },
    { min: 16, message: '密钥长度至少为16个字符', trigger: 'blur' }
  ]
}

// 对话框状态
const secretDialog = reactive({
  visible: false,
  isEdit: false,
  currentAppId: ''
})

// 加载所有应用密钥数据
const loadAppSecrets = async () => {
  loading.value = true
  try {
    const response = await getAllAppSecrets()
    if (response.data && response.data.node && response.data.node.nodes) {
      appSecrets.value = response.data.node.nodes.map(node => {
        const secretData = JSON.parse(node.value)
        return {
          appId: node.key.split('/').pop(),
          secret: secretData.secret || '',
          showSecret: false
        }
      })
    } else {
      appSecrets.value = []
    }
  } catch (error) {
    console.error('加载应用密钥数据失败:', error)
    ElMessage.error('加载应用密钥数据失败')
  } finally {
    loading.value = false
  }
}

// 显示添加密钥对话框
const showAddSecretDialog = () => {
  secretDialog.isEdit = false
  secretDialog.currentAppId = ''
  Object.assign(secretForm, {
    appId: '',
    secret: ''
  })
  showSecretInput.value = true
  secretDialog.visible = true
}

// 显示编辑密钥对话框
const editSecret = (row) => {
  secretDialog.isEdit = true
  secretDialog.currentAppId = row.appId
  Object.assign(secretForm, {
    appId: row.appId,
    secret: row.secret
  })
  showSecretInput.value = false
  secretDialog.visible = true
}

// 保存密钥
const saveSecret = async () => {
  if (!secretFormRef.value) return
  
  await secretFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    saving.value = true
    try {
      const appId = secretDialog.isEdit ? secretDialog.currentAppId : secretForm.appId
      const secretData = {
        appId: appId,
        secret: secretForm.secret
      }
      
      await setAppSecret(appId, secretData)
      ElMessage.success(secretDialog.isEdit ? 'Token更新成功' : 'Token添加成功')
      secretDialog.visible = false
      loadAppSecrets()
    } catch (error) {
      console.error('保存Token失败:', error)
      ElMessage.error('保存Token失败')
    } finally {
      saving.value = false
    }
  })
}

// 确认删除密钥
const confirmDeleteSecret = (row) => {
  ElMessageBox.confirm(
    `确定要删除应用 ${row.appId} 的Token吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteAppSecret(row.appId)
      ElMessage.success('Token删除成功')
      loadAppSecrets()
    } catch (error) {
      console.error('删除Token失败:', error)
      ElMessage.error('删除Token失败')
    }
  }).catch(() => {
    // 用户取消删除操作
  })
}

// 切换密钥显示/隐藏
const toggleSecretVisibility = (row) => {
  row.showSecret = !row.showSecret
}

// 掩码显示密钥
const maskSecret = (secret) => {
  if (!secret) return ''
  if (secret.length <= 8) return '*'.repeat(secret.length)
  return secret.substring(0, 4) + '*'.repeat(secret.length - 8) + secret.substring(secret.length - 4)
}

// 生成随机密钥
const generateSecret = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+'
  let result = ''
  const length = 32
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  secretForm.secret = result
}

onMounted(() => {
  loadAppSecrets()
})
</script>

<style scoped>
.app-secrets-container {
  padding: 20px 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.secret-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
