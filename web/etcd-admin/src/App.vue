<template>
  <div class="app-container">
    <el-container>
      <!-- 侧边导航菜单 -->
      <el-aside :width="isCollapse ? '64px' : '220px'" class="sidebar">
        <div class="logo-container">
          <img src="./assets/etcd-logo.svg" alt="ETCD Logo" class="logo" v-if="!isCollapse" />
          <img src="./assets/etcd-icon.svg" alt="ETCD Icon" class="logo-small" v-else />
        </div>
        
        <el-menu
          router
          :default-active="$route.path"
          class="el-menu-vertical"
          :collapse="isCollapse"
          background-color="#001529"
          text-color="#b3b3b3"
          active-text-color="#ffffff">
          
          <el-menu-item index="/">
            <el-icon><el-icon-odometer /></el-icon>
            <template #title>仪表盘</template>
          </el-menu-item>
          
          <el-menu-item index="/nodes">
            <el-icon><el-icon-cpu /></el-icon>
            <template #title>节点管理</template>
          </el-menu-item>
          
          <el-menu-item index="/app-secrets">
            <el-icon><el-icon-key /></el-icon>
            <template #title>Token 管理</template>
          </el-menu-item>
        </el-menu>
        
        <div class="collapse-btn" @click="toggleCollapse">
          <el-icon v-if="isCollapse"><el-icon-arrow-right /></el-icon>
          <el-icon v-else><el-icon-arrow-left /></el-icon>
        </div>
      </el-aside>
      
      <el-container class="main-container">
        <!-- 顶部导航栏 -->
        <el-header height="60px" class="header">
          <div class="header-left">
            <h2>ETCD 管理系统</h2>
          </div>
          <div class="header-right">
            <el-tooltip content="刷新数据" placement="bottom">
              <el-button circle @click="refreshData">
                <el-icon><el-icon-refresh /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="帮助文档" placement="bottom">
              <el-button circle>
                <el-icon><el-icon-question /></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </el-header>
        
        <!-- 主要内容区域 -->
        <el-main>
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="$route.path === '/nodes'">节点管理</el-breadcrumb-item>
            <el-breadcrumb-item v-if="$route.path === '/app-secrets'">Token 管理</el-breadcrumb-item>
          </el-breadcrumb>
          
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" @refresh="refreshData" />
            </transition>
          </router-view>
        </el-main>
        
        <!-- 底部信息 -->
        <el-footer height="40px" class="footer">
          <div>© {{ new Date().getFullYear() }} ETCD 管理系统 | 版本 1.0.0</div>
        </el-footer>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, provide } from 'vue'
import { ElIcon } from 'element-plus'
import { 
  Odometer as ElIconOdometer, 
  Cpu as ElIconCpu, 
  Key as ElIconKey,
  ArrowLeft as ElIconArrowLeft,
  ArrowRight as ElIconArrowRight,
  Refresh as ElIconRefresh,
  QuestionFilled as ElIconQuestion
} from '@element-plus/icons-vue'

const isCollapse = ref(false)

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const refreshData = () => {
  window.location.reload()
}

// 提供刷新方法给子组件
provide('refreshData', refreshData)
</script>

<style>
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --danger-color: #f5222d;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --border-color: #e8e8e8;
  --background-color: #f0f2f5;
  --sidebar-bg: #001529;
  --sidebar-text: #b3b3b3;
  --header-bg: #ffffff;
  --transition-duration: 0.3s;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  color: var(--text-color);
  background-color: var(--background-color);
}

.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 侧边栏样式 */
.sidebar {
  background-color: var(--sidebar-bg);
  transition: width var(--transition-duration);
  position: relative;
  overflow: hidden;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  z-index: 10;
}

.logo-container {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  height: 40px;
  max-width: 180px;
}

.logo-small {
  height: 32px;
  width: 32px;
}

.el-menu-vertical {
  border-right: none;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 220px;
}

.collapse-btn {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  color: var(--sidebar-text);
  cursor: pointer;
  padding: 8px 0;
  transition: all var(--transition-duration);
}

.collapse-btn:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

/* 头部样式 */
.header {
  background-color: var(--header-bg);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 9;
}

.header-left h2 {
  margin: 0;
  color: var(--primary-color);
  font-weight: 600;
}

.header-right {
  display: flex;
  gap: 10px;
}

/* 主要内容区域样式 */
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.el-main {
  padding: 20px;
  background-color: var(--background-color);
  overflow-y: auto;
}

.breadcrumb {
  margin-bottom: 20px;
}

/* 底部样式 */
.footer {
  background-color: var(--header-bg);
  color: var(--text-color-secondary);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  border-top: 1px solid var(--border-color);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: absolute;
    height: 100%;
    z-index: 1001;
  }
  
  .header-left h2 {
    font-size: 18px;
  }
}
</style>
