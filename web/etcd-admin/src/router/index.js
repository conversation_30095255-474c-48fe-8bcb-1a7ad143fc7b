import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/nodes',
    name: 'Nodes',
    component: () => import('../views/Nodes.vue')
  },
  {
    path: '/app-secrets',
    name: 'AppSecrets',
    component: () => import('../views/AppSecrets.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
