import axios from 'axios'
import { ETCD_SERVER } from '../config'

// 创建axios实例
const api = axios.create({
  baseURL: ETCD_SERVER,
  timeout: 10000
})

// 节点前缀
const NODE_PREFIX = '/v2/keys/nodes'
// 应用密钥前缀
const APP_SECRET_PREFIX = '/v2/keys/app_secrets'

/**
 * 获取所有节点
 */
export function getAllNodes() {
  return api.get(`${NODE_PREFIX}?recursive=true`)
}

/**
 * 获取单个节点
 * @param {string} nodeId 节点ID
 */
export function getNode(nodeId) {
  return api.get(`${NODE_PREFIX}/${nodeId}`)
}

/**
 * 创建或更新节点
 * @param {string} nodeId 节点ID
 * @param {object} nodeData 节点数据
 * @param {number} ttl 过期时间（秒）
 */
export function setNode(nodeId, nodeData, ttl = 60) {
  const params = new URLSearchParams()
  params.append('value', JSON.stringify(nodeData))
  if (ttl > 0) {
    params.append('ttl', ttl)
  }
  return api.put(`${NODE_PREFIX}/${nodeId}`, params)
}

/**
 * 删除节点
 * @param {string} nodeId 节点ID
 */
export function deleteNode(nodeId) {
  return api.delete(`${NODE_PREFIX}/${nodeId}`)
}

/**
 * 获取所有应用密钥
 */
export function getAllAppSecrets() {
  return api.get(`${APP_SECRET_PREFIX}?recursive=true`)
}

/**
 * 获取单个应用密钥
 * @param {string} appId 应用ID
 */
export function getAppSecret(appId) {
  return api.get(`${APP_SECRET_PREFIX}/${appId}`)
}

/**
 * 创建或更新应用密钥
 * @param {string} appId 应用ID
 * @param {object} secretData 密钥数据
 */
export function setAppSecret(appId, secretData) {
  const params = new URLSearchParams()
  params.append('value', JSON.stringify(secretData))
  return api.put(`${APP_SECRET_PREFIX}/${appId}`, params)
}

/**
 * 删除应用密钥
 * @param {string} appId 应用ID
 */
export function deleteAppSecret(appId) {
  return api.delete(`${APP_SECRET_PREFIX}/${appId}`)
}

export default {
  getAllNodes,
  getNode,
  setNode,
  deleteNode,
  getAllAppSecrets,
  getAppSecret,
  setAppSecret,
  deleteAppSecret
}
