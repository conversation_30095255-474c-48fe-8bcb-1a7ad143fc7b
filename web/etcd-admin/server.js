const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// 静态文件服务
app.use(express.static(path.join(__dirname, 'dist')));

// 所有请求都返回 index.html（处理前端路由）
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`ETCD管理系统静态文件服务器运行在 http://localhost:${PORT}`);
  console.log(`前端将直接连接到配置的 ETCD 服务器`);
});
