{"name": "etcd-admin", "version": "1.0.0", "description": "ETCD管理前端，用于管理节点信息和Token信息", "main": "index.js", "engines": {"node": ">=14.0.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "node server.js", "serve": "pnpm run build && node server.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "element-plus": "^2.4.2", "express": "^4.18.2", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "lightningcss": "^1.16.0", "terser": "^5.14.2", "vite": "^4.4.5"}}